// Payment-related functions extracted from payment.js to reduce file size

function formatDateDMY(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
}

function calculateTotalPaid(payments) {
    return payments.reduce((total, payment) => {
        return total + parseFloat(payment.amount);
    }, 0);
}

// New function to calculate remaining amount considering first payment
function calculateRemainingAmount(totalPrice, firstPayment, paidAmount) {
    // Subtract both first payment and paid amounts from total price
    return totalPrice - firstPayment - paidAmount;
}

function updatePaymentStatus(paid, total, statusElement) {
    if (paid >= total) {
        statusElement.textContent = 'مكتمل';
        statusElement.className = 'badge bg-success';
    } else if (paid > 0) {
        statusElement.textContent = 'قيد السداد';
        statusElement.className = 'badge bg-warning';
    } else {
        statusElement.textContent = 'لم يبدأ';
        statusElement.className = 'badge bg-danger';
    }
}

function updatePaymentHistory(payments, historyBodyElement) {
    historyBodyElement.innerHTML = '';
    
    if (payments.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="6" class="text-center">لا توجد مدفوعات مسجلة</td>`;
        historyBodyElement.appendChild(row);
        return;
    }
    
    // Sort payments by date (newest first)
    const sortedPayments = [...payments].sort((a, b) => {
        return new Date(b.date) - new Date(a.date);
    });
    
    // Add each payment to the table
    sortedPayments.forEach((payment, index) => {
        const row = document.createElement('tr');
        const paymentDate = new Date(payment.date);
        const formattedDate = formatDateDMY(paymentDate);
        
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${formattedDate}</td>
            <td>${formatCurrency(payment.amount)} دينار</td>
            <td>${payment.method}</td>
            <td>${payment.receiptNumber}</td>
            <td>${payment.notes || '-'}</td>
        `;
        
        historyBodyElement.appendChild(row);
    });
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ').format(amount);
}

// New Payment Plan Functions
const paymentPlanFunctions = {
    calculateMonthlyPayment: function(totalPrice, downPayment, months, finalPayment = 0) {
        const amountToFinance = totalPrice - downPayment - finalPayment;
        return amountToFinance / months;
    },
    
    generatePaymentSchedule: function(client, plan) {
        if (!client) return [];
        
        // Get existing payments
        let payments = JSON.parse(localStorage.getItem('payments')) || {};
        let clientPayments = payments[client.bookingNumber] || [];
        
        // Create schedule array
        let schedule = [];
        
        // Add first payment
        const bookingDate = new Date(client.bookingDate);
        schedule.push({
            date: bookingDate,
            amount: plan.firstPayment,
            type: 'الدفعة الأولى',
            status: 'مدفوع',
            isPaid: true
        });
        
        // Add monthly payments
        let runningBalance = plan.totalPrice - plan.firstPayment;
        
        for (let i = 0; i < plan.monthsCount; i++) {
            const paymentDate = new Date(bookingDate);
            paymentDate.setMonth(paymentDate.getMonth() + i + 1);
            
            // Check if payment is already made
            const matchingPayment = clientPayments.find(payment => {
                const paymentMonth = new Date(payment.date).getMonth();
                const paymentYear = new Date(payment.date).getFullYear();
                return paymentMonth === paymentDate.getMonth() && 
                       paymentYear === paymentDate.getFullYear();
            });
            
            const isPaid = !!matchingPayment;
            const actualAmount = matchingPayment ? parseFloat(matchingPayment.amount) : plan.monthlyAmount;
            
            // Update running balance
            if (isPaid) {
                runningBalance -= actualAmount;
            }
            
            schedule.push({
                date: paymentDate,
                amount: actualAmount,
                type: 'قسط شهري',
                status: isPaid ? 'مدفوع' : 'غير مدفوع',
                isPaid: isPaid,
                runningBalance: runningBalance
            });
        }
        
        // Add final payment
        const finalPaymentDate = new Date(bookingDate);
        finalPaymentDate.setMonth(finalPaymentDate.getMonth() + plan.monthsCount + 1);
        
        runningBalance = Math.max(0, runningBalance - plan.finalPayment);
        
        schedule.push({
            date: finalPaymentDate,
            amount: plan.finalPayment,
            type: 'دفعة نهائية',
            status: 'غير مدفوع',
            isPaid: false,
            runningBalance: runningBalance
        });
        
        return schedule;
    },
    
    savePlanToStorage: function(bookingNumber, plan) {
        // Get all payment plans
        let paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        
        // Update payment plan for current client
        paymentPlans[bookingNumber] = {
            ...plan,
            lastUpdated: new Date().toISOString()
        };
        
        // Save back to localStorage
        localStorage.setItem('paymentPlans', JSON.stringify(paymentPlans));
    },
    
    recordPayment: function(bookingNumber, payment) {
        // Get all payments
        let payments = JSON.parse(localStorage.getItem('payments')) || {};
        
        // Initialize payments for this booking if not exists
        if (!payments[bookingNumber]) {
            payments[bookingNumber] = [];
        }
        
        // Add payment to payments array
        payments[bookingNumber].push(payment);
        
        // Save back to localStorage
        localStorage.setItem('payments', JSON.stringify(payments));
        
        return payments[bookingNumber];
    }
};