document.addEventListener('DOMContentLoaded', function() {
    const bookingForm = document.getElementById('bookingForm');
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    const bookingNumberElement = document.getElementById('bookingNumber');
    
    // Form submission handler
    bookingForm.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Validate form
        if (validateForm()) {
            // Gather form data
            const bookingData = {
                fullName: document.getElementById('fullName').value.trim(),
                lastName: document.getElementById('lastName').value.trim() || 'غير محدد',
                motherName: document.getElementById('motherName').value.trim() || 'غير محدد',
                idNumber: document.getElementById('idNumber').value.trim(),
                residenceCardNumber: document.getElementById('residenceCardNumber').value.trim() || 'غير محدد',
                phone: document.getElementById('phone').value.trim() || 'غير محدد',
                secondPhone: document.getElementById('secondPhone').value.trim() || 'غير محدد',
                fullAddress: document.getElementById('fullAddress').value.trim() || 'غير محدد',
                customerSource: document.getElementById('customerSource').value || 'غير محدد',
                bookingDate: document.getElementById('bookingDate').value || new Date().toISOString().split('T')[0],
                bookingNumber: generateBookingNumber(),
                bookingTimestamp: new Date().toISOString(),
                // Add unit info data
                areaName: document.getElementById('areaName').value.trim() || 'غير محدد',
                unitNumber: document.getElementById('unitNumber').value.trim() || 'غير محدد',
                buildingNumber: document.getElementById('buildingNumber').value.trim() || 'غير محدد',
                totalArea: document.getElementById('totalArea').value || 'غير محدد',
                actualArea: document.getElementById('actualArea').value || 'غير محدد',
                unitDetails: document.getElementById('unitDetails').value.trim() || 'غير محدد',
                unitClassification: document.getElementById('unitClassification').value || 'غير محدد',
                // Add payment info data
                paymentMethod: document.getElementById('paymentMethod').value || 'غير محدد',
                exemption: document.querySelector('input[name="exemption"]:checked')?.value || 'لا',
                firstPayment: document.getElementById('firstPayment').value || 'غير محدد',
                unitPrice: document.getElementById('unitPrice').value || 'غير محدد',
            };
            
            // Save booking data to localStorage
            saveBooking(bookingData);
            
            // Display booking number in modal
            bookingNumberElement.textContent = bookingData.bookingNumber;
            
            // Show success notification if available
            if (window.showNotification) {
                window.showNotification('تم حفظ البيانات بنجاح! رقم الحجز: ' + bookingData.bookingNumber, 'success', 5000);
            } else {
                // Fallback if notification.js is not loaded
                alert('تم حفظ البيانات بنجاح! رقم الحجز: ' + bookingData.bookingNumber);
            }
            
            // Show success modal
            setTimeout(() => {
                successModal.show();
                bookingForm.reset();
            }, 1000);
        }
    });
    
    // Form validation function
    function validateForm() {
        let isValid = true;
        
        // Get form values
        const fullName = document.getElementById('fullName').value.trim();
        const idNumber = document.getElementById('idNumber').value.trim();
        
        // Basic validation for only essential fields
        if (!fullName) {
            highlightError('fullName', 'الرجاء إدخال الاسم الكامل');
            isValid = false;
        } else {
            removeError('fullName');
        }
        
        if (!idNumber) {
            highlightError('idNumber', 'الرجاء إدخال رقم الهوية');
            isValid = false;
        } else {
            removeError('idNumber');
        }
        
        return isValid;
    }
    
    // Helper function to highlight form errors
    function highlightError(fieldId, message) {
        const field = document.getElementById(fieldId);
        field.classList.add('is-invalid');
        
        // Check if error message already exists
        let errorDiv = field.nextElementSibling;
        if (!errorDiv || !errorDiv.classList.contains('invalid-feedback')) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.insertBefore(errorDiv, field.nextSibling);
        }
        
        errorDiv.textContent = message;
    }
    
    // Helper function to remove error highlights
    function removeError(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.remove('is-invalid');
        
        // Remove error message if it exists
        const errorDiv = field.nextElementSibling;
        if (errorDiv && errorDiv.classList.contains('invalid-feedback')) {
            errorDiv.textContent = '';
        }
    }
    
    // Helper function to format dates in DD-MM-YYYY format
    function formatDate(dateString) {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    }

    // Generate a random booking reference number
    function generateBookingNumber() {
        const prefix = 'MAM';
        const timestamp = new Date().getTime().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${prefix}-${timestamp}-${random}`;
    }
    
    // Save booking to localStorage with formatted dates
    function saveBooking(bookingData) {
        // Format the booking date for display
        if (bookingData.bookingDate) {
            bookingData.formattedBookingDate = formatDate(bookingData.bookingDate);
        }
        
        // Get existing bookings
        let bookings = JSON.parse(localStorage.getItem('bookings')) || [];
        
        // Add new booking
        bookings.push(bookingData);
        
        // Save back to localStorage
        localStorage.setItem('bookings', JSON.stringify(bookings));
        
        // Log success for debugging
        console.log('تم حفظ الحجز رقم: ' + bookingData.bookingNumber);
    }
});