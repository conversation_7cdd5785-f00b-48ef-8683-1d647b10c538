<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - منصة التعلم الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعلم الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">العودة للرئيسية</a>
            </div>
        </div>
    </nav>

    <!-- Login Form -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="form-container">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-circle fa-4x text-primary mb-3"></i>
                            <h2 class="fw-bold">تسجيل الدخول</h2>
                            <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                        </div>

                        <form id="login-form">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" required 
                                           placeholder="أدخل بريدك الإلكتروني">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" required 
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember-me">
                                <label class="form-check-label" for="remember-me">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>

                            <div class="text-center mb-3">
                                <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>

                            <hr class="my-4">

                            <div class="text-center">
                                <p class="mb-3">أو سجل الدخول باستخدام:</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-outline-primary flex-fill">
                                        <i class="fab fa-google me-2"></i>
                                        Google
                                    </button>
                                    <button type="button" class="btn btn-outline-info flex-fill">
                                        <i class="fab fa-facebook me-2"></i>
                                        Facebook
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="register.html" class="text-decoration-none fw-bold">
                                    إنشاء حساب جديد
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">استعادة كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور</p>
                    <form id="forgot-password-form">
                        <div class="mb-3">
                            <label for="reset-email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="reset-email" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                إرسال رابط الاستعادة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Users Info -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>للتجربة - بيانات المستخدمين التجريبيين:</h6>
                    <div class="row">
                        <div class="col-6">
                            <strong>طالب:</strong><br>
                            البريد: <EMAIL><br>
                            كلمة المرور: 123456
                        </div>
                        <div class="col-6">
                            <strong>مدرس:</strong><br>
                            البريد: <EMAIL><br>
                            كلمة المرور: 123456
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data.js"></script>
    <script>
        // Login form handling
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            
            // Demo login validation
            if (validateLogin(email, password)) {
                // Create user session
                const user = createUserSession(email);
                
                // Save to localStorage
                if (rememberMe) {
                    localStorage.setItem('currentUser', JSON.stringify(user));
                } else {
                    sessionStorage.setItem('currentUser', JSON.stringify(user));
                }
                
                // Show success message
                showAlert('تم تسجيل الدخول بنجاح!', 'success');
                
                // Redirect after delay
                setTimeout(() => {
                    window.location.href = user.role === 'teacher' ? 'dashboard.html' : 'profile.html';
                }, 1500);
            } else {
                showAlert('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'danger');
            }
        });
        
        function validateLogin(email, password) {
            // Demo validation
            const validUsers = [
                { email: '<EMAIL>', password: '123456', role: 'student' },
                { email: '<EMAIL>', password: '123456', role: 'teacher' },
                { email: '<EMAIL>', password: '123456', role: 'admin' }
            ];
            
            return validUsers.some(user => user.email === email && user.password === password);
        }
        
        function createUserSession(email) {
            const userMap = {
                '<EMAIL>': {
                    id: 1,
                    name: 'محمد أحمد',
                    email: email,
                    role: 'student',
                    avatar: 'https://via.placeholder.com/100x100/4285f4/ffffff?text=م',
                    enrolledCourses: [1, 2],
                    completedCourses: [],
                    certificates: [],
                    points: 150,
                    level: 'مبتدئ'
                },
                '<EMAIL>': {
                    id: 2,
                    name: 'أحمد محمد',
                    email: email,
                    role: 'teacher',
                    avatar: 'https://via.placeholder.com/100x100/28a745/ffffff?text=أ',
                    courses: [1, 4],
                    students: 1250,
                    rating: 4.8
                },
                '<EMAIL>': {
                    id: 3,
                    name: 'المدير العام',
                    email: email,
                    role: 'admin',
                    avatar: 'https://via.placeholder.com/100x100/dc3545/ffffff?text=إ'
                }
            };
            
            return userMap[email] || userMap['<EMAIL>'];
        }
        
        // Toggle password visibility
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Forgot password form
        document.getElementById('forgot-password-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('reset-email').value;
            
            // Simulate sending reset email
            showAlert('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
            modal.hide();
            
            // Clear form
            this.reset();
        });
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.form-container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Auto-fill demo credentials
        function fillDemoCredentials(type) {
            if (type === 'student') {
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = '123456';
            } else if (type === 'teacher') {
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = '123456';
            }
        }
        
        // Add click handlers for demo info
        document.addEventListener('click', function(e) {
            if (e.target.closest('.alert-info')) {
                const text = e.target.textContent;
                if (text.includes('طالب:')) {
                    fillDemoCredentials('student');
                } else if (text.includes('مدرس:')) {
                    fillDemoCredentials('teacher');
                }
            }
        });
    </script>
</body>
</html>
