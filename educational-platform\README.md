# منصة التعلم الإلكتروني 🎓

منصة تعليمية متكاملة مبنية بـ HTML, CSS, و JavaScript تهدف إلى توفير تجربة تعليمية شاملة ومتميزة.

## 🌟 المميزات الرئيسية

### 📚 إدارة الدورات
- عرض شامل للدورات التعليمية
- تصنيف الدورات حسب المجال والمستوى
- نظام بحث وفلترة متقدم
- صفحات تفصيلية لكل دورة
- نظام تسجيل في الدورات

### 👥 إدارة المستخدمين
- تسجيل دخول وإنشاء حسابات جديدة
- أنواع مختلفة من المستخدمين (طلاب، مدرسين)
- ملفات شخصية للمستخدمين
- نظام مصادقة آمن

### 📝 نظام الاختبارات
- اختبارات تفاعلية متعددة الخيارات
- مؤقت زمني للاختبارات
- نتائج فورية مع التقييم
- إمكانية إعادة الاختبار
- تتبع التقدم

### 🏆 الشهادات المعتمدة
- إصدار شهادات رقمية معتمدة
- نظام التحقق من صحة الشهادات
- تحميل الشهادات بصيغة PDF
- مشاركة الشهادات على وسائل التواصل

### 💬 منتدى النقاش
- منتدى تفاعلي للطلاب والمدرسين
- إنشاء مواضيع جديدة
- نظام الردود والتعليقات
- تصنيف المواضيع
- نظام الإعجاب والمشاركة

### 📱 تصميم متجاوب
- متوافق مع جميع الأجهزة
- تصميم حديث وجذاب
- واجهة مستخدم سهلة الاستخدام
- دعم اللغة العربية بالكامل

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **JavaScript**: التفاعل والوظائف الديناميكية
- **Bootstrap 5**: إطار عمل CSS للتصميم المتجاوب
- **Font Awesome**: الأيقونات
- **Local Storage**: حفظ البيانات محلياً

## 📁 هيكل المشروع

```
educational-platform/
├── index.html              # الصفحة الرئيسية
├── courses.html            # صفحة الدورات
├── course-details.html     # تفاصيل الدورة
├── login.html              # تسجيل الدخول
├── register.html           # إنشاء حساب
├── quiz.html               # صفحة الاختبارات
├── forum.html              # منتدى النقاش
├── certificates.html       # الشهادات
├── styles.css              # ملف التنسيق الرئيسي
├── script.js               # ملف JavaScript الرئيسي
├── data.js                 # البيانات الوهمية
└── README.md               # ملف التوثيق
```

## 🚀 كيفية التشغيل

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd educational-platform
   ```

2. **فتح المشروع**
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي مثل Live Server في VS Code

3. **تجربة المنصة**
   - تصفح الدورات المتاحة
   - سجل حساب جديد أو استخدم الحسابات التجريبية
   - جرب الاختبارات والحصول على الشهادات

## 👤 الحسابات التجريبية

### حساب طالب
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### حساب مدرس
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### حساب مدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## 📋 الصفحات المتاحة

### 🏠 الصفحة الرئيسية (`index.html`)
- عرض المميزات الرئيسية
- الدورات الأكثر شعبية
- إحصائيات المنصة
- روابط سريعة

### 📚 صفحة الدورات (`courses.html`)
- عرض جميع الدورات
- فلترة حسب التصنيف والمستوى والسعر
- بحث في الدورات
- ترتيب النتائج

### 📖 تفاصيل الدورة (`course-details.html`)
- معلومات شاملة عن الدورة
- محتوى الدورة والدروس
- تقييمات الطلاب
- التسجيل في الدورة

### 🔐 تسجيل الدخول (`login.html`)
- نموذج تسجيل دخول آمن
- خيارات تذكر المستخدم
- استعادة كلمة المرور
- تسجيل دخول بوسائل التواصل

### ✍️ إنشاء حساب (`register.html`)
- نموذج تسجيل شامل
- التحقق من قوة كلمة المرور
- اختيار نوع الحساب (طالب/مدرس)
- الموافقة على الشروط والأحكام

### 📝 الاختبارات (`quiz.html`)
- اختبارات تفاعلية
- مؤقت زمني
- تتبع التقدم
- نتائج فورية

### 💬 المنتدى (`forum.html`)
- مواضيع النقاش
- إنشاء مواضيع جديدة
- نظام الردود
- تصنيف المواضيع

### 🏆 الشهادات (`certificates.html`)
- عرض الشهادات المكتسبة
- التحقق من صحة الشهادات
- تحميل ومشاركة الشهادات
- نماذج من الشهادات

## 🎨 التخصيص

### تغيير الألوان
يمكنك تخصيص ألوان المنصة من خلال تعديل متغيرات CSS في ملف `styles.css`:

```css
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
}
```

### إضافة دورات جديدة
أضف دورات جديدة في ملف `data.js`:

```javascript
const newCourse = {
  id: 7,
  title: "اسم الدورة",
  description: "وصف الدورة",
  instructor: "اسم المدرس",
  price: "السعر",
  // ... باقي الخصائص
};

coursesData.push(newCourse);
```

## 🔧 التطوير المستقبلي

### المميزات المخططة
- [ ] نظام دفع إلكتروني
- [ ] مشغل فيديو متقدم
- [ ] تطبيق هاتف محمول
- [ ] نظام إشعارات
- [ ] لوحة تحكم للمدرسين
- [ ] تقارير وإحصائيات متقدمة
- [ ] نظام رسائل خاصة
- [ ] تكامل مع وسائل التواصل الاجتماعي

### التحسينات التقنية
- [ ] تحويل إلى Progressive Web App (PWA)
- [ ] إضافة قاعدة بيانات حقيقية
- [ ] تطوير API خلفي
- [ ] تحسين الأداء والسرعة
- [ ] إضافة اختبارات تلقائية
- [ ] تحسين الأمان

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير المنصة! يمكنكم:

1. **الإبلاغ عن الأخطاء**: افتحوا issue جديد
2. **اقتراح مميزات**: شاركوا أفكاركم
3. **تطوير الكود**: أرسلوا Pull Request
4. **تحسين التوثيق**: ساعدوا في تحسين الشرح

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://educational-platform.com
- **تويتر**: @edu_platform
- **فيسبوك**: /educational.platform

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.

---

**ملاحظة**: هذا مشروع تعليمي وتجريبي. البيانات المستخدمة وهمية لأغراض العرض فقط.

## 📊 إحصائيات المشروع

- **عدد الصفحات**: 8 صفحات رئيسية
- **عدد الدورات**: 6 دورات تجريبية
- **عدد المميزات**: 20+ ميزة
- **اللغات المدعومة**: العربية (مع إمكانية إضافة لغات أخرى)
- **المتصفحات المدعومة**: جميع المتصفحات الحديثة

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة التعليم الإلكتروني**
