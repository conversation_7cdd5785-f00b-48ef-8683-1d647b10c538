// New file to handle monthly payment status functionality
document.addEventListener('DOMContentLoaded', function() {
    // Update the status of a monthly installment
    window.updateInstallmentStatus = function(bookingNumber, installmentIndex, isPaid) {
        // Get payment plans
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[bookingNumber];
        
        if (!plan) return false;
        
        // Get installment statuses
        plan.installmentStatuses = plan.installmentStatuses || [];
        
        // Update status
        while (plan.installmentStatuses.length <= installmentIndex) {
            plan.installmentStatuses.push(false);
        }
        
        plan.installmentStatuses[installmentIndex] = isPaid;
        
        // Save back to localStorage
        localStorage.setItem('paymentPlans', JSON.stringify(paymentPlans));
        
        return true;
    };
    
    // Get the status of a monthly installment
    window.getInstallmentStatus = function(bookingNumber, installmentIndex) {
        // Get payment plans
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[bookingNumber];
        
        if (!plan || !plan.installmentStatuses || installmentIndex >= plan.installmentStatuses.length) {
            return false;
        }
        
        return plan.installmentStatuses[installmentIndex];
    };
    
    // Get all installment statuses for a booking
    window.getAllInstallmentStatuses = function(bookingNumber) {
        // Get payment plans
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[bookingNumber];
        
        if (!plan || !plan.installmentStatuses) {
            return [];
        }
        
        return plan.installmentStatuses;
    };
});