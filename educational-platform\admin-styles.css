/* Admin Dashboard Styles */
:root {
    --admin-primary: #667eea;
    --admin-secondary: #764ba2;
    --admin-success: #28a745;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-info: #17a2b8;
    --admin-light: #f8f9fa;
    --admin-dark: #343a40;
    --sidebar-width: 280px;
    --topbar-height: 70px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f6fa;
    direction: rtl;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-right-color: var(--admin-warning);
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 15px;
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.top-bar {
    background: white;
    padding: 0 30px;
    height: var(--topbar-height);
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.content-area {
    padding: 30px;
}

/* Sections */
.section {
    display: none;
}

.section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 20px;
}

.stat-icon.bg-primary { background: var(--admin-primary); }
.stat-icon.bg-success { background: var(--admin-success); }
.stat-icon.bg-warning { background: var(--admin-warning); }
.stat-icon.bg-info { background: var(--admin-info); }

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Form Controls */
.form-control,
.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Buttons */
.btn {
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--admin-primary), var(--admin-secondary));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Theme Preview */
.theme-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    min-height: 200px;
}

.preview-header {
    height: 30px;
    background: var(--admin-primary);
    border-radius: 5px;
    margin-bottom: 10px;
    position: relative;
}

.preview-nav {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.preview-hero {
    height: 60px;
    background: linear-gradient(45deg, var(--admin-primary), var(--admin-secondary));
    border-radius: 5px;
    margin-bottom: 10px;
}

.preview-section {
    height: 20px;
    background: #e9ecef;
    border-radius: 3px;
    margin-bottom: 10px;
}

.preview-cards {
    display: flex;
    gap: 5px;
}

.preview-card {
    flex: 1;
    height: 40px;
    background: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

/* Content Editor */
.content-editor {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    min-height: 300px;
}

.editor-toolbar {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.editor-content {
    padding: 20px;
}

/* Media Manager */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.media-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.media-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.media-item img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.upload-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--admin-primary);
    background: rgba(102, 126, 234, 0.05);
}

/* Image Manager Sections */
.image-manager-section {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.image-slot {
    position: relative;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.image-slot:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.image-slot img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.image-slot .image-overlay {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 40px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.image-slot:hover .image-overlay {
    opacity: 1;
}

.image-slot .image-overlay .btn {
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.image-slot:hover .image-overlay .btn {
    transform: scale(1);
}

/* Quick Actions */
.quick-actions .btn {
    justify-content: flex-start;
    text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .content-area {
        padding: 20px 15px;
    }
    
    .top-bar {
        padding: 0 15px;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
}

/* Color Picker */
.form-control-color {
    width: 60px;
    height: 40px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    cursor: pointer;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--admin-success);
    border-left: 4px solid var(--admin-success);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--admin-danger);
    border-left: 4px solid var(--admin-danger);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--admin-warning);
}

/* Media Item Actions */
.media-item {
    position: relative;
}

.media-actions {
    position: absolute;
    top: 5px;
    left: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-item:hover .media-actions {
    opacity: 1;
}

.media-name {
    font-size: 0.8rem;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Table Styles */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table th {
    background: var(--admin-light);
    border: none;
    font-weight: 600;
    color: var(--admin-dark);
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* Modal Enhancements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.modal-header {
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 20px 25px;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: var(--admin-primary);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* File Details */
.file-details h6 {
    color: var(--admin-dark);
    margin-bottom: 15px;
}

.file-details p {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

/* Security Options */
.security-option {
    padding: 15px;
    background: var(--admin-light);
    border-radius: 10px;
    margin-bottom: 10px;
}

.form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* Backup Options */
.backup-options {
    background: var(--admin-light);
    padding: 15px;
    border-radius: 10px;
}

.backup-options .form-check {
    margin-bottom: 10px;
}

/* Storage Info */
.storage-info {
    text-align: center;
}

/* Animation for loading states */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--admin-primary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--admin-secondary);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background: #2d3748;
        color: white;
    }

    .form-control,
    .form-select {
        background: #4a5568;
        border-color: #4a5568;
        color: white;
    }

    .table {
        background: #2d3748;
        color: white;
    }

    .table th {
        background: #4a5568;
    }
}
