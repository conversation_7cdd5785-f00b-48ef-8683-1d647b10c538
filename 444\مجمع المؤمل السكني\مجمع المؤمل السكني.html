<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجمع المؤمل السكني</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            font-size: 14px;
            background-color:#f0f0f0 ;           
        }

        #app {
            max-width: 800px;
            margin: 0 auto;
            background-color:#ffffff ;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
            background-image: url(cop+.jpg);

        }

        h1 {
            text-align: center;
            color: #ffffff;
            font-size: 2em;
            margin-bottom: 15px;
        }

        h2 {
            background-color: #146444;
            padding: 6px;
            border-radius: 3px;
            margin-top: 15px;
            color: #fff;
            font-size: 1.4em;
            border: 3px solid #73c6b6 ;
        }

        .form-group {
            margin-bottom: 10px;
            padding: 6px;
            border-radius: 3px;
        }

        label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            font-size: 0.9em;
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        select,
        textarea {
            width: 100%;
            padding: 6px;
            border: 2px solid #146444;
            border-radius: 3px;
            box-sizing: border-box;
            margin-top: 3px;
            font-size: 0.9em;
        }

        textarea {
            height: 80px;
        }

        button {
            background-color: #146444;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background-color: #7dcea0;
            color: #17202a;
        }

        .submission-message {
            margin-top: 15px;
            padding: 12px;
            background-color: #1f618d;
            color: #155724;
            border: 2px solid #146444;
            border-radius: 3px;
            text-align: center;
            font-size: 0.9em;
        }

        /* Header Styles */
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #146444;
            padding-bottom: 15px;
        }

        .logo-container {
           margin-bottom: 10px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #ffffff;
            display: block;
            margin-bottom: 5px;
        }
        .buttons-container {
           margin-top: 20px;
           text-align: center;
           
        }
        .date-container {
            font-size: 16px;
            color: #ffffff;
            font-weight: bold;
        }

        /* Form Grid Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        /* Section Colors */
        .form-grid .form-group:nth-child(odd) {
            background-color: #73c6b6;
            border: 3px solid #146444 ;
        }

        .form-grid .form-group:nth-child(even) {
            background-color: #73c6b6;
            border: 3px solid #146444 ;

        }

        .important-notes {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8d7da;
            border: 2px solid #146444;
            color: #000;
            border-radius: 4px;
        }

        .important-notes h2 {
            color: #ffffff;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .important-notes ul {
            padding-left: 20px;
            list-style-type: disc;
        }

        .important-notes li {
            margin-bottom: 5px;
            font-size: 0.85em;
        }



        @media print {
            body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            font-size: 14px;
            background-color:#f0f0f0 ;   
                    
        }

        #app {
            max-width: 800px;
            margin: 0 auto;
            background-color:#ffffff ;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
            background-image: url(cop+.jpg);

        }

        h1 {
            text-align: center;
            color: #ffffff;
            font-size: 2em;

        }

        h2 {
            background-color: #146444;
            padding: 6px;
            border-radius: 3px;
            color: #fff;
            font-size: 1.4em;
            border: 3px solid #73c6b6 ;
        }

        .form-group {
            padding: 1px;
            border-radius: 3px;
        }

        label {
            display: block;
            font-weight: bold;
            font-size: 0.9em;
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        select,
        textarea {
            width: 100%;
            padding: 6px;
            border: 2px solid #146444;
            border-radius: 3px;
            box-sizing: border-box;
            font-size: 0.9em;
        }

        textarea {
            height: 80px;
        }

        button {
           display: none;
        }

        

        .submission-message {
            padding: 12px;
            background-color: #1f618d;
            color: #155724;
            border: 2px solid #146444;
            border-radius: 3px;
            text-align: center;
            font-size: 0.9em;
        }

        /* Header Styles */
        .header {
            text-align: center;
            border-bottom: 2px solid #146444;
            padding-bottom: 15px;
        }

        .logo-container {
           margin-bottom: 10px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #ffffff;
            display: block;
        }
        .buttons-container {
           text-align: center;
           
        }
        .date-container {
            font-size: 16px;
            color: #ffffff;
            font-weight: bold;
        }

        /* Form Grid Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        /* Section Colors */
        .form-grid .form-group:nth-child(odd) {
            background-color: #73c6b6;
            border: 3px solid #146444 ;
        }

        .form-grid .form-group:nth-child(even) {
            background-color: #73c6b6;
            border: 3px solid #146444 ;

        }

        .important-notes {
            padding: 15px;
            background-color: #f8d7da;
            border: 2px solid #146444;
            color: #000;
            border-radius: 4px;
        }

        .important-notes h2 {
            color: #ffffff;
            font-size: 1.2em;
        }

        .important-notes ul {
            padding-left: 20px;
            list-style-type: disc;
        }

        .important-notes li {
            font-size: 0.85em;
        }
            /* Ensure even distribution in grid */
            .form-grid > div {
                width: 100%;
            }
        }

        .customer-info,
        .unit-info,
        .payment-info {
            break-inside: avoid;
        }
    </style>
    <script>
        window.esmsInitOptions = {
            shimMode: true,
        };
    </script>
    <script src="https://ga.jspm.io/npm:es-module-shims@1.8.0/dist/es-module-shims.js"></script>
    <script type="importmap">
        {
          "imports": {
            "vue": "https://unpkg.com/vue@3.4.15/dist/vue.esm-browser.js"
          }
        }
    </script>
</head>
<body>
    <div id="app">
     
        <header class="header">
            <div class="logo-container">
            <img src="logo+ copy.jpg" style="width: 150px; height: 150px; border-radius: 40px; border: 4px solid #146444;">
            <img src="images.png" style="width: 100px; height: 100px; border-radius: 20px; border: 4px solid #146444;">
            </div>

            <span class="company-name">مجمع المؤمل السكني</span>
            <div class="date-container">
                تاريخ الإدخال: {{ currentDate }}
            </div>
        </header>

        <h1>حجز وحدة سكنية</h1>

        <form @submit.prevent="handleSubmit">
            <h2>معلومات الزبون</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label for="customerName">اسم الزبون الثلاثي:</label>
                    <input type="text" id="customerName" v-model="formData.customerName" required>
                </div>

                <div class="form-group">
                    <label for="motherName">اسم الأم الثلاثي:</label>
                    <input type="text" id="motherName" v-model="formData.motherName" required>
                </div>

                <div class="form-group">
                    <label for="idNumber">رقم الهوية:</label>
                    <input type="text" id="idNumber" v-model="formData.idNumber" required>
                </div>

                <div class="form-group">
                    <label for="address">العنوان الكامل:</label>
                    <input type="text" id="address" v-model="formData.address" required>
                </div>

                <div class="form-group">
                    <label for="title">اللقب:</label>
                    <input type="text" id="title" v-model="formData.title">
                </div>

                <div class="form-group">
                    <label for="phone1">رقم الهاتف الأول:</label>
                    <input type="tel" id="phone1" v-model="formData.phone1" required>
                </div>

                <div class="form-group">
                    <label for="phone2">رقم الهاتف الثاني:</label>
                    <input type="tel" id="phone2" v-model="formData.phone2">
                </div>

                 <div class="form-group">
                    <label for="residenceCardNumber">رقم البطاقة السكن:</label>
                    <input type="text" id="residenceCardNumber" v-model="formData.residenceCardNumber">
                </div>
            </div>

            <h2>معلومات الوحدة السكنية</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label for="areaName">اسم المنطقة:</label>
                    <input type="text" id="areaName" v-model="formData.areaName" required>
                </div>

                <div class="form-group">
                    <label for="buildingNumber">رقم المبنى:</label>
                    <input type="text" id="buildingNumber" v-model="formData.buildingNumber" required>
                </div>

                <div class="form-group">
                    <label for="actualUnitArea">المساحة الفعلية للوحدة:</label>
                    <input type="text" id="actualUnitArea" v-model="formData.actualUnitArea" required>
                </div>

                <div class="form-group">
                    <label for="unitType">نوع الوحدة السكنية:</label>
                    <select id="unitType" v-model="formData.unitType" required>
                        <option value="">اختر نوع الوحدة</option>
                        <option value="شقة">شقة</option>
                        <option value="فيلا">فيلا</option>
                        <option value="تاون هاوس">تاون هاوس</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="unitNumber">رقم الوحدة السكنية:</label>
                    <input type="text" id="unitNumber" v-model="formData.unitNumber" required>
                </div>

                <div class="form-group">
                    <label for="totalUnitArea">المساحة الإجمالية للوحدة:</label>
                    <input type="text" id="totalUnitArea" v-model="formData.totalUnitArea" required>
                </div>

                <div class="form-group">
                    <label for="unitClassification">تصنيف الوحدة السكنية:</label>
                    <input type="text" id="unitClassification" v-model="formData.unitClassification" required>
                </div>
            </div>

            <h2>معلومات التسديد</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label for="paymentMethod">طريقة التسديد:</label>
                    <select id="paymentMethod" v-model="formData.paymentMethod" required>
                        <option value="">اختر طريقة التسديد</option>
                        <option value="كاش">كاش</option>
                        <option value="اقساط">اقساط</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="initialPayment">الدفعة الأولى الواجب دفعها:</label>
                    <input type="number" id="initialPayment" v-model="formData.initialPayment" required>
                </div>

                <div class="form-group">
                    <label for="exception">استثناء (نعم/لا):</label>
                    <select id="exception" v-model="formData.exception">
                        <option value="لا">لا</option>
                        <option value="نعم">نعم</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="unitPrice" >سعر الوحدة السكنية الكامل:</label>
                    <input type="number"style="width: 170px;"  id="unitPrice" v-model="formData.unitPrice" required>
                </div>
            </div>

            <div class="form-group">
                <label for="notes">ملاحظات:</label>
                <textarea id="notes" v-model="formData.notes"></textarea>
            </div>

            <div class="buttons-container">
                <button type="submit" style="font-size: 16px;font-weight: bold;">إرسال الحجز على الواتس أب</button>
                <button type="button" style="margin: 50px;font-size: 16px;font-weight: bold;" @click="printForm">طباعة</button>
                <button type="button" style="font-size: 16px;font-weight: bold;"  @click="clearForm">مسح النموذج</button>
            </div>
            


        </form>
        <div class="important-notes">
            <h2>ملاحظات هامة</h2>
            <ul>
                <li>لا تعتبر هذه الاستمارة بمثابة عقد ولا جزء من عقد البيع ولا يترتب أي التزامات على الشركة.</li>
                <li>الحجز ساري فقط لمدة يومان فقط بعد تاريخ الحجز اعلاه و بعدها سيتم الغاء الحجز تلقائيا في حالة لم يتم تسديد الدفعة الاولى لشراء الوحدة السكنية.</li>
                <li>يحظر على المشتري التنازل عن استمارة حجز الوحدة السكنية للغير ويعد التنازل باطلاً.</li>
                <li>في حالة لتعاقد بالوكالة يقر الوكيل بان البيانات الواردة بالنسبة له وللمشتري صحيحة وان وكالته سارية المفعيل ويكون هو المسؤول عما ورد من ببانات.</li>
                <li>يقر الزبون انه اطلع على عقد البيع وعقد الخدمات وانه أحاط علماً بكافة بنوده وابدى استعداده لتوقيع العقد بعد تسديد الدفعة الاولى لشراء الوحدة السكنية ويقر بعلمه بتقديم الضمانات المتفق عليا بالمبالغ المتبقية من خلال تقديم (صكوك وكمبيالات مصدفة)</li>
                <li>يقر الزبون بعلمه أن عمولة الحجز البالغة ( 1.000.000 د.ع ) هي عمولة أدارية لتنظيم وتثبيت الحجز وأن المبلغ غير قابل للرد حتى وأن لم يتم التعاقد وتم الغاء الحجز :هو مبلغ مقطوع لا يدخل ضمن سعر الوحدة السكنية .</li>
                <li>في حال تسديد دفعة الاولى لشراء الوحدة السكنية في أحد المصارف المعتمدة من قبل الشركة يجير احضار وصل التسديد ضمن فترة سريان الحجز أعلاه وبخلافه يمكن للشركة التصرف بالوحدة السكنية و تعويض الزبون بوحدة سكنية بديلة دون اعتراض الزبون على ان لا تتعدى مدة احضار الوصل اكثر من (15) يوما من تاريخ الاستمارة و بخلافه يسقط حق الزبون نهائيا و يتم ارجاع مبلغ الدفعة الاولى فقط الى الزبون.</li>
                <li>في حالة رغبة الزبون بالانسحاب و عدم اكمال اجراءات التعاقد يتم استرجاع المبالغ المسددة المتمثلة بدفعة التسجيل خلال مدة (شهرين).</li>
                <li>اني الموقع ادناه و المذكورة معلوماتي اعلاه اقر بأني قد قرأت هذه الاستمارة و تفاصيلها و اطلعت على كافة بنودها و اتعهد بعدم المطالبة بأسترجاع عمولة الحجز كونه غير قابل للرد ، و اتعهد بدفع الدفعة الاولى لشراء الوحدة السكنية خلال مدة يومان فقط من تاريخ هذه الاستمارة و بخلافه يسقط حقي بالمطالبة بالوحدة السكنية دون الرجوع الى المحاكم المختصة.</li>
            </ul>
        </div>
        
    </div>
    <script type="module">
        import { createApp } from 'vue';

        createApp({
            data() {
                return {
                    formData: {
                        customerName: '',
                        motherName: '',
                        idNumber: '',
                        address: '',
                        title: '',
                        phone1: '',
                        phone2: '',
                        residenceCardNumber: '',
                        areaName: '',
                        buildingNumber: '',
                        actualUnitArea: '',
                        unitType: '',
                        unitNumber: '',
                        totalUnitArea: '',
                        unitClassification: '',
                        notes: '',
                        paymentMethod: '',
                        initialPayment: '',
                        exception: 'لا',
                        unitPrice: ''
                    },
                    submissionMessage: '',
                    currentDate: new Date().toLocaleDateString()
                }
            },
            methods: {
                handleSubmit() {
                    // Construct the WhatsApp message
                    let message = "حجز وحدة سكنية:\n\n";
                    for (const key in this.formData) {
                        message += `${key}: ${this.formData[key]}\n`;
                    }

                    // Encode the message for URL
                    const encodedMessage = encodeURIComponent(message);

                    // Create the WhatsApp URL
                    const whatsappURL = `https://wa.me/${this.formData.phone1}?text=${encodedMessage}`;

                    // Open WhatsApp in a new tab
                    window.open(whatsappURL, '_blank');


                    // In a real application, you would send this data to a server
                    console.log('Form Data:', this.formData);

                    // Display a submission message
                    this.submissionMessage = 'تم إرسال الحجز بنجاح!';

                    // Clear the form (optional)
                    this.formData = {
                        customerName: '',
                        motherName: '',
                        idNumber: '',
                        address: '',
                        title: '',
                        phone1: '',
                        phone2: '',
                        residenceCardNumber: '',
                        areaName: '',
                        buildingNumber: '',
                        actualUnitArea: '',
                        unitType: '',
                        unitNumber: '',
                        totalUnitArea: '',
                        unitClassification: '',
                        notes: '',
                        paymentMethod: '',
                        initialPayment: '',
                        exception: 'لا',
                        unitPrice: ''
                    };

                    // Clear the message after 3 seconds
                    setTimeout(() => {
                        this.submissionMessage = '';
                    }, 3000);
                },
                clearForm() {
            // Clear all form fields
            Object.keys(this.formData).forEach(key => {
                if (key === 'exception') {
                    this.formData[key] = 'لا'; // Reset exception to default
                } else {
                    this.formData[key] = '';
                }
            });
            this.submissionMessage = ''; // Clear any existing messages
             // Optionally scroll to top
            window.scrollTo(0, 0);
            console.log('Form Cleared');
        },
                printForm() {
                    console.log('Print button clicked!');
                    window.print();
                }
            }
        }).mount('#app');
    </script>
</body>
</html>