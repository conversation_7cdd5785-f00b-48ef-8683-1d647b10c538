<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجمع المؤمل السكني - قاعدة البيانات</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <svg width="100" height="100" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="45" fill="#1a6e93" />
                    <rect x="30" y="25" width="15" height="50" fill="white" />
                    <rect x="55" y="25" width="15" height="50" fill="white" />
                    <rect x="30" y="25" width="40" height="15" fill="white" />
                </svg>
                <h1>مجمع المؤمل السكني</h1>
            </div>
            <p class="subtitle">نوفر لكم أفضل الوحدات السكنية بمواصفات عصرية</p>
        </header>

        <nav class="main-nav mb-4">
            <ul class="nav nav-pills nav-fill">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-home me-2"></i>تسجيل حجز</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="database.html"><i class="fas fa-database me-2"></i>قاعدة البيانات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="receipt.html"><i class="fas fa-receipt me-2"></i>طباعة الوصل</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="payment.html"><i class="fas fa-money-bill-wave me-2"></i>بوابة دفع الاقساط</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="monthly_payment.html"><i class="fas fa-calendar-alt me-2"></i>دفع القسط الشهري</a>
                </li>
            </ul>
        </nav>

        <main>
            <div class="card database-section">
                <div class="card-header bg-primary text-white">
                    <h2><i class="fas fa-database me-2"></i>قاعدة بيانات الحجوزات</h2>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث عن حجز...">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary" id="exportBtn">
                                        <i class="fas fa-file-export me-1"></i> تصدير البيانات
                                    </button>
                                    <button class="btn btn-outline-danger" id="clearDbBtn">
                                        <i class="fas fa-trash me-1"></i> مسح البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-primary">
                                <tr>
                                    <th>رقم الحجز</th>
                                    <th>الاسم الكامل</th>
                                    <th>رقم الهوية</th>
                                    <th>رقم الهاتف</th>
                                    <th>نوع الوحدة</th>
                                    <th>عدد الغرف</th>
                                    <th>تاريخ الحجز</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="bookingsTableBody">
                                <!-- سيتم ملؤها بواسطة جافاسكريبت -->
                            </tbody>
                        </table>
                    </div>

                    <div id="emptyMessage" class="text-center my-5 d-none">
                        <i class="fas fa-database fa-3x mb-3 text-muted"></i>
                        <p class="lead">لا توجد حجوزات مسجلة بعد</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4" id="pagination">
                            <!-- سيتم إنشاؤها بواسطة جافاسكريبت -->
                        </ul>
                    </nav>
                </div>
            </div>
        </main>

        <!-- Modal for Booking Details -->
        <div class="modal fade" id="bookingDetailsModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الحجز</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="bookingDetailsContent">
                        <!-- سيتم ملؤها بواسطة جافاسكريبت -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" id="printReceiptBtn">
                            <i class="fas fa-print me-1"></i> طباعة الوصل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal for Delete -->
        <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تأكيد الحذف</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من رغبتك في حذف هذا الحجز؟</p>
                        <p>رقم الحجز: <span id="deleteBookingNumber"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <div class="contact-info">
                <p><i class="fas fa-phone me-2"></i>هاتف: ************</p>
                <p><i class="fas fa-envelope me-2"></i>البريد الإلكتروني: <EMAIL></p>
            </div>
            <p class="copyright">جميع الحقوق محفوظة &copy; مجمع المؤمل السكني 2023</p>
        </footer>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
</body>
</html>