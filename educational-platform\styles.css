/* ===== CSS VARIABLES ===== */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --bg-dark: #111827;
    
    --border-light: #e5e7eb;
    --border-dark: #374151;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-light {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* ===== TOP BAR ===== */
.top-bar {
    background: linear-gradient(135deg, var(--text-dark), #374151);
    color: white;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1050;
    transition: var(--transition-normal);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.9;
}

.contact-item i {
    font-size: 0.75rem;
    color: var(--accent-color);
}

.top-social-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.follow-text {
    opacity: 0.8;
    font-size: 0.8rem;
}

.top-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    color: white;
    font-size: 0.75rem;
    transition: var(--transition-fast);
}

.top-social-link.facebook { background: #3b5998; }
.top-social-link.twitter { background: #1da1f2; }
.top-social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c); }
.top-social-link.linkedin { background: #0077b5; }
.top-social-link.youtube { background: #ff0000; }

.top-social-link:hover {
    transform: scale(1.1);
    color: white;
}

.admin-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: var(--radius-md);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.admin-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.admin-link i {
    font-size: 0.75rem;
}

/* ===== TOP BAR TOGGLE ===== */
.top-bar-toggle {
    position: fixed;
    top: 24px;
    right: 20px;
    z-index: 1051;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 30px;
    height: 20px;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: var(--transition-fast);
    cursor: pointer;
}

.top-bar-toggle:hover {
    background: rgba(0, 0, 0, 0.9);
}

.top-bar.hidden {
    transform: translateY(-100%);
}

.top-bar.hidden + .top-bar-toggle {
    top: 10px;
}

/* ===== NAVIGATION ===== */
.premium-navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    padding: 1rem 0;
    position: fixed;
    top: 40px;
    left: 0;
    right: 0;
    z-index: 1040;
    transition: var(--transition-normal);
}

.navbar.scrolled {
    top: 0;
    box-shadow: var(--shadow-lg);
}

.premium-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-dark);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-wrapper {
    position: relative;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    position: relative;
    z-index: 2;
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.1; }
}

.brand-content {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    line-height: 1;
}

.brand-tagline {
    font-size: 0.875rem;
    color: var(--text-light);
    line-height: 1;
}

/* ===== NAVIGATION MENU ===== */
.navbar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-dark);
    font-weight: 500;
    position: relative;
    transition: var(--transition-fast);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link:hover .nav-indicator,
.nav-link.active .nav-indicator {
    width: 80%;
}

/* ===== MEGA DROPDOWN ===== */
.mega-dropdown {
    background: white;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 2rem;
    margin-top: 1rem;
    min-width: 600px;
}

.mega-title {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-light);
}

.mega-link {
    display: block;
    padding: 0.5rem 0;
    color: var(--text-light);
    transition: var(--transition-fast);
}

.mega-link:hover {
    color: var(--primary-color);
    padding-right: 0.5rem;
}

.mega-featured {
    background: var(--bg-light);
    padding: 1rem;
    border-radius: var(--radius-md);
}

.featured-course img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
}

.featured-course h6 {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.featured-course p {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0;
}

/* ===== NAVBAR END ===== */
.navbar-end {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-container {
    position: relative;
}

.search-box-premium {
    display: flex;
    align-items: center;
    background: var(--bg-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-fast);
}

.search-box-premium:focus-within {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-premium {
    border: none;
    background: transparent;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    width: 200px;
    outline: none;
}

.search-btn-premium {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn-premium:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.auth-section {
    display: flex;
    gap: 0.5rem;
}

.btn-auth {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.btn-login {
    color: var(--primary-color);
    background: transparent;
    border: 1px solid var(--primary-color);
}

.btn-login:hover {
    background: var(--primary-color);
    color: white;
}

.btn-register {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-register:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

/* ===== SLIDER TOGGLE ===== */
.slider-toggle-container {
    position: fixed;
    top: 120px;
    right: 20px;
    z-index: 1030;
}

.slider-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.slider-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.slider-toggle-btn i {
    font-size: 1rem;
}

/* ===== HERO SLIDER ===== */
.hero-slider-section {
    margin-top: 80px;
    position: relative;
}

.hero-slide {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background-attachment: fixed;
}

.hero-content {
    color: white;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image img {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

/* ===== CAROUSEL CONTROLS ===== */
.custom-indicators {
    bottom: 30px;
}

.custom-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    transition: var(--transition-fast);
}

.custom-indicators button.active {
    background: white;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    transition: var(--transition-fast);
}

.carousel-control-prev {
    left: 30px;
}

.carousel-control-next {
    right: 30px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(0, 0, 0, 0.5);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 20px;
    height: 20px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
    .top-bar-content {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .contact-info {
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .top-bar {
        font-size: 0.75rem;
        padding: 0.25rem 0;
    }
    
    .contact-info {
        display: none;
    }
    
    .premium-navbar {
        top: 30px;
        padding: 0.75rem 0;
    }
    
    .brand-title {
        font-size: 1rem;
    }
    
    .brand-tagline {
        font-size: 0.75rem;
    }
    
    .search-input-premium {
        width: 150px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .top-bar-toggle {
        top: 20px;
        right: 10px;
    }
    
    .slider-toggle-container {
        top: 100px;
        right: 10px;
    }
    
    .slider-toggle-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }
    
    .carousel-control-prev {
        left: 15px;
    }
    
    .carousel-control-next {
        right: 15px;
    }
}

/* ===== SECTIONS COMMON STYLES ===== */
section {
    padding: 100px 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 20px;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ===== FEATURES SECTION ===== */
.features-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(102,126,234,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(118,75,162,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,215,0,0.1)"/></svg>') repeat;
    pointer-events: none;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    z-index: 2;
}

.icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    z-index: -1;
}

.feature-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.feature-link a {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition-fast);
}

.feature-link a:hover {
    color: var(--secondary-color);
    gap: 12px;
}

/* ===== POPULAR COURSES SECTION ===== */
.popular-courses-section {
    background: white;
    position: relative;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.course-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
    position: relative;
}

.course-card.premium {
    border: 2px solid var(--primary-color);
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.course-card:hover .course-image img {
    transform: scale(1.1);
}

.course-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.course-badge.new {
    background: var(--success-color);
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.preview-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: white;
    color: var(--text-dark);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.preview-btn:hover {
    background: var(--primary-color);
    color: white;
}

.course-content {
    padding: 25px;
}

.course-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.course-category,
.course-level {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.course-category {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.course-level {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.course-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
    line-height: 1.3;
}

.course-description {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 20px;
}

.course-instructor {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.course-instructor img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.course-instructor span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.course-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.course-stats .stat {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-muted);
    font-size: 0.85rem;
}

.course-stats .stat i {
    color: var(--warning-color);
}

.course-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-light);
}

.course-price {
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    font-size: 1rem;
    color: var(--text-muted);
    text-decoration: line-through;
}

.enroll-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.enroll-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.section-footer {
    text-align: center;
}

.view-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: transparent;
    color: var(--primary-color);
    padding: 15px 30px;
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.view-all-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* ===== EXPERT INSTRUCTORS SECTION ===== */
.expert-instructors-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.expert-instructors-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(102,126,234,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(118,75,162,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,215,0,0.1)"/></svg>') repeat;
    pointer-events: none;
}

.instructors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.instructor-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.instructor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.instructor-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.instructor-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 25px;
}

.instructor-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.avatar-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.social-overlay {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s ease;
}

.instructor-card:hover .social-overlay {
    opacity: 1;
    bottom: 10px;
}

.social-link {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.social-link.linkedin { background: #0077b5; }
.social-link.twitter { background: #1da1f2; }
.social-link.github { background: #333; }
.social-link.behance { background: #1769ff; }
.social-link.dribbble { background: #ea4c89; }
.social-link.facebook { background: #3b5998; }
.social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743); }

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.instructor-info {
    text-align: center;
}

.instructor-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.instructor-title {
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 15px;
}

.instructor-credentials {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.credential {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.instructor-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.instructor-stats .stat {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.instructor-stats .stat i {
    color: var(--warning-color);
}

.instructors-cta {
    text-align: center;
    padding: 40px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 20px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
}

.instructors-cta p {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 20px;
}

.btn-instructor-apply {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-instructor-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

/* ===== SUCCESS STORIES SECTION ===== */
.success-stories-section {
    padding: 100px 0;
    background: white;
    position: relative;
}

.stories-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 80px;
}

.story-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.story-card.featured {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.02);
}

.story-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.story-card.featured:hover {
    transform: scale(1.02) translateY(-5px);
}

.story-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.student-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.success-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 25px;
    height: 25px;
    background: #ffd700;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 0.7rem;
}

.student-info h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.student-info p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 8px;
}

.achievement {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    font-weight: 500;
}

.story-card.featured .achievement {
    color: #ffd700;
}

.story-card:not(.featured) .achievement {
    color: var(--primary-color);
}

.story-content blockquote {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
    position: relative;
    padding-left: 20px;
}

.story-content blockquote::before {
    content: '"';
    position: absolute;
    left: 0;
    top: -10px;
    font-size: 3rem;
    opacity: 0.3;
    font-family: serif;
}

.story-metrics {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.metric {
    text-align: center;
}

.metric .number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffd700;
}

.metric .label {
    font-size: 0.8rem;
    opacity: 0.8;
}

.stories-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    padding: 60px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
}

.stories-stats .stat-item {
    text-align: center;
    padding: 20px;
}

.stories-stats .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

.stories-stats .stat-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stories-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stories-stats .stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

/* ===== CALL TO ACTION SECTION ===== */
.cta-section {
    padding: 120px 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

.cta-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.cta-shapes .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 15s infinite ease-in-out;
}

.cta-shapes .shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.cta-shapes .shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 5s;
}

.cta-shapes .shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 70%;
    animation-delay: 10s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(20px) rotate(240deg); }
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.cta-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 30px;
    position: relative;
    animation: pulse 3s infinite;
}

.icon-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulse 3s infinite 1s;
}

.cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.cta-description {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.cta-features .feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 500;
}

.cta-features .feature i {
    color: #ffd700;
    font-size: 1.2rem;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.btn-primary-cta {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: #ffd700;
    color: #333;
    padding: 18px 35px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-primary-cta:hover {
    background: #ffed4e;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
    color: #333;
}

.btn-secondary-cta {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 18px 35px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary-cta:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    color: white;
}

.cta-guarantee {
    display: flex;
    justify-content: center;
}

.guarantee-badge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.guarantee-badge i {
    color: #ffd700;
    font-size: 1.1rem;
}

/* ===== REVOLUTIONARY FOOTER ===== */
.revolutionary-footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

/* Wave Background */
.wave-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120px;
    z-index: 1;
}

.wave-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.shape-fill {
    fill: #667eea;
}

.wave-1 { animation: wave 8s ease-in-out infinite; }
.wave-2 { animation: wave 6s ease-in-out infinite reverse; }
.wave-3 { animation: wave 4s ease-in-out infinite; }

@keyframes wave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-25px); }
}

/* Newsletter CTA */
.newsletter-cta {
    padding: 80px 0;
    position: relative;
    z-index: 2;
}

.cta-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.cta-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.cta-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
}

.cta-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.newsletter-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-container {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.email-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    outline: none;
}

.submit-btn {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.form-benefits {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.benefit-item i {
    color: #ffd700;
}

/* Main Footer Content */
.footer-main {
    padding: 80px 0 40px;
    position: relative;
    z-index: 2;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 60px;
}

/* Brand Section */
.footer-brand-section {
    padding-right: 40px;
}

.brand-logo-footer {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.logo-container {
    position: relative;
    width: 60px;
    height: 60px;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    position: relative;
    z-index: 2;
}

.logo-rings {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: pulse 3s infinite;
}

.ring-1 {
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    animation-delay: 0s;
}

.ring-2 {
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    animation-delay: 1s;
}

.ring-3 {
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    animation-delay: 2s;
}

.brand-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-slogan {
    font-size: 0.9rem;
    opacity: 0.8;
}

.brand-description {
    font-size: 1rem;
    line-height: 1.7;
    opacity: 0.9;
    margin-bottom: 30px;
}

/* Achievement Counters */
.achievement-counters {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.counter-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.counter-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 5px;
}

.counter-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Trust Badges */
.trust-badges {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
}

.badge-item i {
    color: #ffd700;
}

/* Footer Links Sections */
.footer-links-section,
.footer-categories-section {
    padding: 0 20px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: #ffd700;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
    padding: 8px 0;
}

.footer-links a:hover {
    color: #ffd700;
    padding-right: 10px;
}

.footer-links a i {
    width: 16px;
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Contact Section */
.footer-contact-section {
    padding-left: 20px;
}

.contact-cards {
    margin-bottom: 40px;
}

.contact-card {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.contact-info h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffd700;
}

.contact-info a,
.contact-info p {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 5px;
    display: block;
}

.contact-info a:hover {
    color: #ffd700;
}

/* Social Media Grid */
.social-media-grid h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffd700;
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.social-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    text-decoration: none;
    transition: var(--transition-fast);
    backdrop-filter: blur(10px);
}

.social-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.social-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.social-card.facebook .social-icon { background: #3b5998; }
.social-card.twitter .social-icon { background: #1da1f2; }
.social-card.instagram .social-icon { background: linear-gradient(45deg, #f09433, #e6683c); }
.social-card.linkedin .social-icon { background: #0077b5; }
.social-card.youtube .social-icon { background: #ff0000; }
.social-card.telegram .social-icon { background: #0088cc; }

.social-info {
    display: flex;
    flex-direction: column;
}

.platform {
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
}

.followers {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Admin Panel Footer */
.admin-panel-footer {
    padding: 40px 0;
    background: rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.admin-panel-card {
    display: flex;
    align-items: center;
    gap: 30px;
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #333;
    position: relative;
}

.admin-content {
    flex: 1;
}

.admin-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffd700;
}

.admin-content p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.admin-access-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.admin-access-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
    background: rgba(0, 0, 0, 0.5);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
}

.bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright-info {
    text-align: center;
}

.copyright-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
}

.made-with-love {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.made-with-love i {
    color: #ff6b6b;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.footer-legal-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition-fast);
}

.footer-legal-links a:hover {
    color: #ffd700;
}

.footer-certifications {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.cert-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.cert-badge i {
    color: #ffd700;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 20%;
    right: 15%;
    animation-delay: 3s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 20%;
    animation-delay: 6s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    bottom: 20%;
    right: 25%;
    animation-delay: 9s;
}

.shape-5 {
    width: 60px;
    height: 60px;
    top: 60%;
    left: 50%;
    animation-delay: 12s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    top: 80%;
    right: 40%;
    animation-delay: 15s;
}

/* ===== RESPONSIVE DESIGN FOR NEW SECTIONS ===== */
@media (max-width: 1200px) {
    .instructors-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .stories-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .story-card.featured {
        transform: none;
    }

    .story-card.featured:hover {
        transform: translateY(-5px);
    }

    .footer-grid {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }

    .cta-wrapper {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
}

@media (max-width: 992px) {
    .features-section,
    .popular-courses-section,
    .expert-instructors-section,
    .success-stories-section,
    .cta-section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }

    .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }

    .instructors-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }

    .instructor-card {
        padding: 25px;
    }

    .stories-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 40px;
    }

    .cta-features {
        gap: 25px;
    }

    .cta-title {
        font-size: 2.5rem;
    }

    .cta-description {
        font-size: 1.2rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-brand-section {
        padding-right: 0;
    }

    .footer-contact-section {
        padding-left: 0;
    }

    .achievement-counters {
        grid-template-columns: repeat(4, 1fr);
    }

    .social-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .features-section,
    .popular-courses-section,
    .expert-instructors-section,
    .success-stories-section,
    .cta-section {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card {
        padding: 30px 20px;
    }

    .courses-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .instructors-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .instructor-card {
        padding: 20px;
    }

    .instructor-avatar {
        width: 100px;
        height: 100px;
    }

    .story-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .story-metrics {
        justify-content: center;
        gap: 20px;
    }

    .stories-stats {
        grid-template-columns: 1fr;
        padding: 30px;
        gap: 20px;
    }

    .cta-features {
        flex-direction: column;
        gap: 15px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-description {
        font-size: 1.1rem;
    }

    .btn-primary-cta,
    .btn-secondary-cta {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .newsletter-cta {
        padding: 60px 0;
    }

    .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .cta-text h2 {
        font-size: 2rem;
    }

    .form-container {
        flex-direction: column;
        gap: 15px;
    }

    .footer-main {
        padding: 60px 0 30px;
    }

    .admin-panel-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-legal-links {
        justify-content: center;
    }

    .footer-certifications {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .features-section,
    .popular-courses-section,
    .expert-instructors-section,
    .success-stories-section,
    .cta-section {
        padding: 40px 0;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .feature-card {
        padding: 25px 15px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .instructor-card {
        padding: 15px;
    }

    .instructor-avatar {
        width: 80px;
        height: 80px;
    }

    .instructor-name {
        font-size: 1.2rem;
    }

    .instructor-stats {
        gap: 15px;
    }

    .story-card {
        padding: 20px;
    }

    .stories-stats .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stories-stats .stat-number {
        font-size: 2rem;
    }

    .cta-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }

    .cta-description {
        font-size: 1rem;
    }

    .guarantee-badge {
        padding: 10px 20px;
        font-size: 0.8rem;
    }

    .newsletter-cta {
        padding: 40px 0;
    }

    .newsletter-form {
        padding: 25px;
    }

    .cta-text h2 {
        font-size: 1.5rem;
    }

    .achievement-counters {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .social-grid {
        grid-template-columns: 1fr;
    }

    .floating-elements .floating-shape {
        display: none;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .top-bar,
    .premium-navbar,
    .slider-toggle-container,
    .revolutionary-footer,
    .floating-elements {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }

    .section-title {
        page-break-after: avoid;
    }

    .feature-card,
    .course-card,
    .instructor-card,
    .story-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
