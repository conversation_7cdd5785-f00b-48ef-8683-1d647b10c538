/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
    --shadow-medium: 0 10px 30px rgba(0,0,0,0.12);
    --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Se<PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    transition: var(--transition);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 15px;
    padding: 8px 16px !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--warning-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
    background: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    color: var(--warning-color) !important;
    background: rgba(255, 255, 255, 0.15);
}

/* Hero Slider Section */
.hero-slider {
    position: relative;
    overflow: hidden;
}

/* Custom Indicators */
.custom-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 100;
}

.custom-indicators button {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 12px 20px;
    color: white;
    font-size: 0.9rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
}

.custom-indicators button.active,
.custom-indicators button:hover {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    border-color: white;
    transform: translateY(-3px);
}

.indicator-number {
    font-weight: 700;
    font-size: 0.8rem;
}

.indicator-text {
    font-weight: 500;
}

/* Progress Bar */
.slide-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 100;
}

.slide-progress .progress-bar {
    height: 100%;
    background: var(--warning-color);
    width: 0%;
    transition: width 6s linear;
}

.hero-slide {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Slide Backgrounds */
.slide-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.slide-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.slide-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

/* Background Shapes */
.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 70%;
    animation-delay: 5s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 80%;
    animation-delay: 10s;
}

.shape-4 {
    width: 80px;
    height: 80px;
    top: 80%;
    right: 20%;
    animation-delay: 15s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(90deg); }
    50% { transform: translateY(-40px) rotate(180deg); }
    75% { transform: translateY(-20px) rotate(270deg); }
}

/* Particles */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.3)"/></svg>') repeat;
    animation: particles 30s infinite linear;
    pointer-events: none;
}

@keyframes particles {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.hero-image-container:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.02);
}

.hero-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition);
}

.hero-image-container:hover .hero-main-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.hero-image-container:hover .image-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary-color);
    cursor: pointer;
    transform: scale(0.8);
    transition: var(--transition);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.hero-image-container:hover .play-button {
    transform: scale(1);
}

.certificate-badge,
.community-badge {
    background: rgba(255, 255, 255, 0.95);
    padding: 12px 24px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--primary-color);
    transform: translateY(20px);
    transition: var(--transition);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.hero-image-container:hover .certificate-badge,
.hero-image-container:hover .community-badge {
    transform: translateY(0);
}

.hero-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.hero-image:hover img {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: float-up-down 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.floating-card span {
    font-weight: 600;
    color: var(--dark-color);
}

.card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 5%;
    animation-delay: 1s;
}

.card-3 {
    top: 40%;
    left: 10%;
    animation-delay: 2s;
}

/* Hero Stats */
.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-mini h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-mini small {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Hero Badge */
.hero-badge .badge {
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 25px;
    padding: 8px 20px;
    box-shadow: var(--shadow-light);
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.8;
    transition: var(--transition);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background: white;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-image: none;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.carousel-indicators {
    bottom: 30px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid white;
    transition: var(--transition);
}

.carousel-indicators button.active {
    background: white;
    transform: scale(1.2);
}

/* Section Titles */
.section-title {
    position: relative;
    margin-bottom: 1rem;
}

.title-underline {
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
    margin-top: 1rem;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.feature-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--gradient-primary);
    border-radius: 50%;
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.feature-card:hover .feature-icon::before {
    width: 100%;
    height: 100%;
}

.feature-icon i {
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.feature-card:hover .feature-icon i {
    color: white;
}

/* Courses Section */
.courses-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.courses-slider {
    position: relative;
    padding: 0 60px;
}

.courses-control-prev,
.courses-control-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    z-index: 10;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.courses-control-prev {
    right: 10px;
}

.courses-control-next {
    left: 10px;
}

.courses-control-prev:hover,
.courses-control-next:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-medium);
}

/* Course Cards */
.course-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
    margin: 0 10px;
    position: relative;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.course-card:hover::before {
    opacity: 0.05;
}

.course-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.course-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.course-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    opacity: 0;
    transition: var(--transition);
}

.course-card:hover .course-image::before {
    opacity: 1;
}

.course-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: var(--transition);
    z-index: 3;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-category {
    margin-bottom: 15px;
}

.course-category .badge {
    margin-left: 5px;
    font-size: 0.75rem;
    padding: 5px 10px;
}

.course-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    line-height: 1.4;
}

.course-description {
    font-size: 0.9rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-instructor img {
    width: 35px;
    height: 35px;
    object-fit: cover;
}

.course-stats {
    background: var(--light-color);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 15px 0;
}

.course-stats .col-4 {
    border-left: 1px solid #dee2e6;
}

.course-stats .col-4:last-child {
    border-left: none;
}

.course-actions .btn {
    font-weight: 600;
    padding: 12px 20px;
}

.course-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: var(--shadow-light);
    z-index: 2;
}

.course-content {
    padding: 25px;
    position: relative;
    z-index: 2;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.rating {
    color: var(--warning-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Instructors Section */
.instructors-section {
    background: white;
}

.instructor-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    text-align: center;
    position: relative;
}

.instructor-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.instructor-image {
    position: relative;
    overflow: hidden;
    height: 250px;
    border-radius: var(--border-radius);
}

.instructor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.instructor-card:hover .instructor-image img {
    transform: scale(1.1);
}

.instructor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

.instructor-card:hover .instructor-overlay {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: white;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    transform: translateY(20px);
}

.instructor-card:hover .social-links a {
    transform: translateY(0);
}

.social-links a:hover {
    background: var(--warning-color);
    color: white;
    transform: scale(1.1);
}

.instructor-info {
    padding: 25px;
}

.instructor-info h5 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.instructor-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    font-size: 0.9rem;
}

.instructor-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
}

/* Statistics Section */
.stats-section {
    background: var(--gradient-primary);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 30s infinite linear;
}

.stat-item {
    padding: 30px 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    transition: var(--transition);
}

.stat-item:hover .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-item h3 {
    margin-bottom: 10px;
    font-weight: 700;
}

.stat-item p {
    margin: 0;
    opacity: 0.9;
}

/* Modern Buttons */
.btn {
    border-radius: 50px;
    padding: 14px 35px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    border: none;
    color: #333;
    box-shadow: var(--shadow-light);
}

.btn-warning:hover {
    background: #e0a800;
    color: #333;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-outline-light {
    border: 2px solid rgba(255,255,255,0.8);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition);
}

.modern-btn:hover::after {
    width: 300px;
    height: 300px;
}

.pulse-btn {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* Forms */
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 40px;
    margin-top: 100px;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Video Player */
.video-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 400px;
}

.video-controls {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 5px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    border-radius: 3px;
    transition: width 0.1s ease;
}

/* Quiz Styles */
.quiz-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.question {
    margin-bottom: 30px;
}

.question h4 {
    margin-bottom: 20px;
    color: #333;
}

.answer-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-option:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.answer-option.selected {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

.answer-option.correct {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

.answer-option.incorrect {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

/* Forum Styles */
.forum-post {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-left: 15px;
}

.post-meta {
    color: #666;
    font-size: 0.9rem;
}

/* Certificate Styles */
.certificate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.certificate::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.certificate-content {
    position: relative;
    z-index: 1;
}

/* Dashboard Styles */
.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.dashboard-stat {
    text-align: center;
    padding: 20px;
}

.dashboard-stat i {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .courses-slider {
        padding: 0 40px;
    }

    .floating-elements {
        display: none;
    }
}

@media (max-width: 992px) {
    .hero-slide {
        text-align: center;
        padding: 120px 0 60px;
    }

    .hero-slide h1 {
        font-size: 2.5rem;
    }

    .courses-slider {
        padding: 0 20px;
    }

    .courses-control-prev,
    .courses-control-next {
        display: none;
    }

    .instructor-card {
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .hero-slide {
        min-height: 80vh;
        padding: 100px 0 40px;
    }

    .hero-slide h1 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .hero-slide .lead {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .hero-slide .btn {
        padding: 12px 25px;
        font-size: 0.9rem;
        margin-bottom: 10px;
        width: 100%;
    }

    .feature-card,
    .course-card {
        margin-bottom: 30px;
    }

    .form-container {
        margin: 80px 15px 20px;
        padding: 30px 20px;
    }

    .stat-item {
        margin-bottom: 30px;
    }

    .stat-item h3 {
        font-size: 2rem;
    }

    .instructor-image {
        height: 200px;
    }

    .course-stats {
        padding: 10px 5px;
    }

    .course-stats .col-4 {
        font-size: 0.8rem;
    }

    .navbar-nav .nav-link {
        margin: 5px 0;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .hero-slide h1 {
        font-size: 1.75rem;
    }

    .display-5 {
        font-size: 1.5rem;
    }

    .courses-slider {
        padding: 0 10px;
    }

    .course-card {
        margin: 0 5px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .instructor-info {
        padding: 20px 15px;
    }

    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes float-up-down {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0,-30px,0);
    }
    70% {
        transform: translate3d(0,-15px,0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Parallax Effect */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.parallax-element {
    will-change: transform;
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Lazy Loading */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Enhanced Hover Effects */
.enhanced-hover {
    position: relative;
    overflow: hidden;
}

.enhanced-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.enhanced-hover:hover::before {
    left: 100%;
}

/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform-origin: left;
    transform: scaleX(0);
    z-index: 9999;
    transition: transform 0.1s ease-out;
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    border-color: var(--primary-color);
}

/* Admin Button in Footer */
.btn-admin-footer {
    background: linear-gradient(45deg, #ffc107, #ff8c00);
    border: none;
    color: #333;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 25px;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    text-decoration: none;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.btn-admin-footer:hover {
    background: linear-gradient(45deg, #ff8c00, #ffc107);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    color: #333;
    text-decoration: none;
}

.btn-admin-footer:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
    color: #333;
    text-decoration: none;
}

.admin-access {
    background: rgba(255, 193, 7, 0.1);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 193, 7, 0.2);
    text-align: center;
}

.admin-access p {
    margin-bottom: 0;
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Revolutionary Footer Styles */
.revolutionary-footer {
    background: linear-gradient(135deg, #0f0c29 0%, #302b63 30%, #24243e 60%, #1a1a2e 100%);
    position: relative;
    overflow: hidden;
    color: white;
}

/* Animated Wave Background */
.wave-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 150px;
    z-index: 1;
}

.wave-svg {
    width: 100%;
    height: 100%;
}

.shape-fill {
    fill: rgba(255, 255, 255, 0.1);
}

.wave-1 {
    animation: waveMove1 8s ease-in-out infinite;
}

.wave-2 {
    animation: waveMove2 6s ease-in-out infinite reverse;
}

.wave-3 {
    animation: waveMove3 10s ease-in-out infinite;
}

@keyframes waveMove1 {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-20px); }
}

@keyframes waveMove2 {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(15px); }
}

@keyframes waveMove3 {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-10px); }
}

/* Newsletter CTA Section */
.newsletter-cta {
    padding: 100px 0;
    position: relative;
    z-index: 2;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(15px);
}

.cta-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 30px;
    padding: 60px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.cta-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.cta-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    position: relative;
    animation: pulse 3s infinite;
}

.cta-icon::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 3s infinite 0.5s;
}

.cta-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-text p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.newsletter-form {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-container {
    position: relative;
    display: flex;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 60px;
    padding: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.form-container:focus-within {
    border-color: #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.email-input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 18px 25px;
    color: white;
    font-size: 1.1rem;
    outline: none;
}

.email-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.submit-btn {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border: none;
    border-radius: 50px;
    padding: 18px 35px;
    color: #333;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition);
    cursor: pointer;
    font-size: 1.1rem;
}

.submit-btn:hover {
    transform: translateX(-8px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
}

.form-benefits {
    display: flex;
    gap: 25px;
    margin-top: 20px;
    justify-content: center;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
}

.benefit-item i {
    color: #ffd700;
    font-size: 1rem;
}

/* Main Footer */
.footer-main {
    padding: 80px 0;
    position: relative;
    z-index: 2;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 60px;
}

/* Brand Section */
.footer-brand-section {
    padding-right: 20px;
}

.brand-logo-footer {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.logo-container {
    position: relative;
    width: 80px;
    height: 80px;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.logo-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 {
    width: 100px;
    height: 100px;
    animation: ringPulse 3s infinite;
}

.ring-2 {
    width: 120px;
    height: 120px;
    animation: ringPulse 3s infinite 1s;
}

.ring-3 {
    width: 140px;
    height: 140px;
    animation: ringPulse 3s infinite 2s;
}

@keyframes ringPulse {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

.brand-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-slogan {
    color: #ffd700;
    font-size: 1rem;
    font-weight: 500;
}

.brand-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.8;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* Achievement Counters */
.achievement-counters {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.counter-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.counter-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

.counter-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
    line-height: 1;
    margin-bottom: 5px;
}

.counter-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Trust Badges */
.trust-badges {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.badge-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.badge-item i {
    color: #ffd700;
    font-size: 1rem;
}

/* Section Titles */
.section-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 2px;
}

.section-title i {
    color: #ffd700;
    font-size: 1.2rem;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 15px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition);
    font-size: 1rem;
    padding: 8px 0;
    position: relative;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    transition: var(--transition);
    border-radius: 0 3px 3px 0;
}

.footer-links a:hover::before {
    width: 4px;
}

.footer-links a:hover {
    color: #ffd700;
    transform: translateX(15px);
}

.footer-links a i {
    font-size: 0.9rem;
    width: 20px;
    color: #ffd700;
    transition: var(--transition);
}

.footer-links a:hover i {
    transform: scale(1.2);
}

/* Contact Cards */
.contact-cards {
    margin-bottom: 30px;
}

.contact-card {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.contact-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
    position: relative;
}

.contact-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.contact-info h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: white;
}

.contact-info a,
.contact-info p {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.95rem;
    margin: 3px 0;
    transition: var(--transition);
    display: block;
}

.contact-info a:hover {
    color: #ffd700;
    transform: translateX(5px);
}

/* Social Media Grid */
.social-media-grid h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.social-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.social-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: var(--transition);
}

.social-card:hover::before {
    left: 100%;
}

.social-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
}

.social-info {
    flex: 1;
}

.platform {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 0.95rem;
    margin-bottom: 2px;
}

.followers {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Social Platform Colors */
.social-card.facebook .social-icon { background: #3b5998; }
.social-card.twitter .social-icon { background: #1da1f2; }
.social-card.instagram .social-icon { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743); }
.social-card.linkedin .social-icon { background: #0077b5; }
.social-card.youtube .social-icon { background: #ff0000; }
.social-card.telegram .social-icon { background: #0088cc; }

.social-card.facebook:hover { background: rgba(59, 89, 152, 0.2); }
.social-card.twitter:hover { background: rgba(29, 161, 242, 0.2); }
.social-card.instagram:hover { background: rgba(240, 148, 51, 0.2); }
.social-card.linkedin:hover { background: rgba(0, 119, 181, 0.2); }
.social-card.youtube:hover { background: rgba(255, 0, 0, 0.2); }
.social-card.telegram:hover { background: rgba(0, 136, 204, 0.2); }

/* Admin Panel Footer */
.admin-panel-footer {
    padding: 40px 0;
    background: rgba(255, 255, 255, 0.03);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-panel-card {
    display: flex;
    align-items: center;
    gap: 25px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.admin-panel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,215,0,0.1), transparent);
    transition: var(--transition);
}

.admin-panel-card:hover::before {
    left: 100%;
}

.admin-panel-card:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.admin-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: #333;
    position: relative;
    flex-shrink: 0;
}

.icon-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.admin-content {
    flex: 1;
}

.admin-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: white;
}

.admin-content p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 1rem;
}

.admin-access-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 15px 25px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    font-size: 1rem;
}

.admin-access-btn:hover {
    background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
    transform: translateX(-8px);
    box-shadow: 0 5px 20px rgba(255, 215, 0, 0.4);
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
    background: rgba(0, 0, 0, 0.4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.bottom-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.copyright-info {
    text-align: center;
}

.copyright-text {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
}

.made-with-love {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.made-with-love i {
    color: #e74c3c;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.footer-legal-links {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
    padding: 5px 10px;
    border-radius: 15px;
}

.footer-legal-links a:hover {
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.footer-certifications {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.cert-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.cert-badge:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.cert-badge i {
    color: #ffd700;
    font-size: 0.9rem;
}

/* Floating Animation Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.02);
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 5%;
    animation-delay: 0s;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 10%;
    animation-delay: 5s;
    background: radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%);
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    right: 60%;
    animation-delay: 10s;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 60%;
    right: 20%;
    animation-delay: 15s;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.08) 0%, transparent 70%);
}

.shape-5 {
    width: 250px;
    height: 250px;
    top: 30%;
    left: 70%;
    animation-delay: 7s;
    background: radial-gradient(circle, rgba(118, 75, 162, 0.08) 0%, transparent 70%);
}

.shape-6 {
    width: 80px;
    height: 80px;
    bottom: 40%;
    left: 30%;
    animation-delay: 12s;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.08) 0%, transparent 70%);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-20px) rotate(270deg);
        opacity: 0.6;
    }
}

/* Revolutionary Footer Responsive */
@media (max-width: 1200px) {
    .footer-grid {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }

    .footer-brand-section {
        grid-column: 1 / -1;
    }

    .cta-wrapper {
        gap: 40px;
        padding: 40px;
    }

    .cta-text h2 {
        font-size: 2rem;
    }
}

@media (max-width: 992px) {
    .cta-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .achievement-counters {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }

    .social-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .bottom-content {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }

    .footer-legal-links {
        order: 2;
    }

    .footer-certifications {
        order: 3;
    }
}

@media (max-width: 768px) {
    .newsletter-cta {
        padding: 60px 0;
    }

    .cta-wrapper {
        padding: 30px;
        border-radius: 20px;
    }

    .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .cta-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .cta-text h2 {
        font-size: 1.8rem;
    }

    .cta-text p {
        font-size: 1rem;
    }

    .newsletter-form {
        padding: 25px;
    }

    .form-container {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .submit-btn {
        width: 100%;
        justify-content: center;
    }

    .form-benefits {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .footer-main {
        padding: 60px 0;
    }

    .brand-logo-footer {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .achievement-counters {
        grid-template-columns: repeat(2, 1fr);
    }

    .trust-badges {
        justify-content: center;
    }

    .section-title {
        justify-content: center;
        text-align: center;
    }

    .contact-cards {
        text-align: center;
    }

    .contact-card {
        flex-direction: column;
        text-align: center;
    }

    .social-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .admin-panel-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .footer-legal-links {
        flex-direction: column;
        gap: 10px;
    }

    .footer-certifications {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        padding: 4px 0;
        font-size: 0.7rem;
    }

    .contact-info {
        gap: 8px;
    }

    .contact-item {
        padding: 2px 6px;
        font-size: 0.65rem;
    }

    .contact-item i {
        width: 14px;
        height: 14px;
        font-size: 0.6rem;
    }

    .top-social-links {
        gap: 6px;
    }

    .follow-text {
        font-size: 0.6rem;
        padding: 1px 4px;
        margin-right: 4px;
    }

    .top-social-link {
        width: 20px;
        height: 20px;
        font-size: 0.6rem;
    }

    .admin-link {
        padding: 2px 6px;
        font-size: 0.6rem;
    }

    .admin-link i {
        font-size: 0.6rem;
        margin-right: 3px;
    }
}

@media (max-width: 576px) {
    .top-bar {
        padding: 3px 0;
        font-size: 0.65rem;
    }

    .contact-info {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .contact-item {
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    .top-social-links {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .follow-text {
        margin-right: 0;
        margin-bottom: 2px;
        font-size: 0.55rem;
    }

    .top-social-link {
        width: 18px;
        height: 18px;
        font-size: 0.55rem;
    }

    .admin-link {
        font-size: 0.55rem;
        padding: 1px 4px;
    }

    .premium-navbar {
        top: 24px;
    }
}

    .cta-text h2 {
        font-size: 1.5rem;
    }

    .cta-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .brand-info h3 {
        font-size: 1.5rem;
    }

    .brand-slogan {
        font-size: 0.9rem;
    }

    .achievement-counters {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .counter-number {
        font-size: 1.5rem;
    }

    .social-grid {
        grid-template-columns: 1fr;
    }

    .floating-shape {
        display: none;
    }
}



.newsletter-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.newsletter-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    animation: pulse 2s infinite;
}

.newsletter-text h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.newsletter-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    line-height: 1.6;
}

.form-group {
    position: relative;
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    padding: 5px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.form-group:focus-within {
    border-color: var(--warning-color);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
}

.newsletter-input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    color: white;
    font-size: 1rem;
    outline: none;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-btn {
    background: var(--gradient-primary);
    border: none;
    border-radius: 50px;
    padding: 15px 30px;
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    cursor: pointer;
}

.newsletter-btn:hover {
    transform: translateX(-5px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.newsletter-benefits {
    display: flex;
    gap: 20px;
    margin-top: 15px;
    justify-content: center;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.benefit i {
    color: var(--warning-color);
}

/* Main Footer */
.footer-main {
    padding: 80px 0;
    position: relative;
    z-index: 2;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 2fr;
    gap: 60px;
}

/* Company Column */
.company-brand {
    margin-bottom: 30px;
}

.brand-logo-footer {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.logo-container {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.logo-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    animation: logoGlow 3s ease-in-out infinite;
}

.brand-text h2 {
    font-size: 1.6rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-motto {
    color: var(--warning-color);
    font-size: 1rem;
    font-weight: 500;
}

.company-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.8;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.company-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--warning-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
}

/* Column Titles */
.column-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.column-title i {
    color: var(--warning-color);
    font-size: 1.1rem;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    font-size: 1rem;
    padding: 8px 0;
}

.footer-links a:hover {
    color: var(--warning-color);
    transform: translateX(10px);
}

.footer-links a i {
    font-size: 0.9rem;
    width: 20px;
    color: var(--warning-color);
}

/* Contact Info */
.contact-info {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: var(--transition);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
}

.contact-details a,
.contact-details p {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.95rem;
    margin: 2px 0;
    transition: var(--transition);
}

.contact-details a:hover {
    color: var(--warning-color);
}

/* Social Media */
.social-media h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.social-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.social-info {
    flex: 1;
}

.platform-name {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
}

.followers-count {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Social Platform Colors */
.social-link.facebook .social-icon { background: #3b5998; }
.social-link.twitter .social-icon { background: #1da1f2; }
.social-link.instagram .social-icon { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743); }
.social-link.linkedin .social-icon { background: #0077b5; }
.social-link.youtube .social-icon { background: #ff0000; }
.social-link.telegram .social-icon { background: #0088cc; }

.social-link.facebook:hover { background: rgba(59, 89, 152, 0.2); }
.social-link.twitter:hover { background: rgba(29, 161, 242, 0.2); }
.social-link.instagram:hover { background: rgba(240, 148, 51, 0.2); }
.social-link.linkedin:hover { background: rgba(0, 119, 181, 0.2); }
.social-link.youtube:hover { background: rgba(255, 0, 0, 0.2); }
.social-link.telegram:hover { background: rgba(0, 136, 204, 0.2); }

/* Admin Panel Section */
.admin-panel-section {
    padding: 40px 0;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-panel-card {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.admin-panel-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.admin-icon {
    width: 60px;
    height: 60px;
    background: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #333;
}

.admin-content {
    flex: 1;
}

.admin-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
}

.admin-content p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.admin-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--warning-color);
    color: #333;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.admin-btn:hover {
    background: #e0a800;
    transform: translateX(-5px);
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.copyright-section {
    text-align: center;
}

.copyright-text {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
}

.powered-by {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.powered-by i {
    color: #e74c3c;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.footer-links {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--warning-color);
}

.trust-badges {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.badge {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.badge i {
    color: var(--warning-color);
}

/* Animated Background */
.footer-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.03);
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 10%;
    animation-delay: 5s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 50%;
    right: 60%;
    animation-delay: 10s;
}

.shape-4 {
    width: 80px;
    height: 80px;
    bottom: 60%;
    right: 20%;
    animation-delay: 15s;
}

.shape-5 {
    width: 120px;
    height: 120px;
    top: 30%;
    left: 70%;
    animation-delay: 7s;
}

.shape-6 {
    width: 60px;
    height: 60px;
    bottom: 40%;
    left: 30%;
    animation-delay: 12s;
}

/* Footer Logo */
.footer-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.footer-logo .logo-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.footer-logo .logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
}

.footer-logo .logo-text h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
}

.footer-logo .logo-text span {
    font-size: 0.9rem;
    color: var(--warning-color);
    font-weight: 500;
}

/* Footer Description */
.footer-desc {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 25px;
    font-size: 0.95rem;
}

/* Footer Stats */
.footer-stats {
    display: flex;
    gap: 25px;
    margin-bottom: 30px;
}

.footer-stats .stat {
    text-align: center;
}

.footer-stats .number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--warning-color);
    line-height: 1;
}

.footer-stats .label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
}

/* Widget Titles */
.widget-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: white;
    position: relative;
    padding-bottom: 10px;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--warning-color);
    border-radius: 2px;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: var(--warning-color);
    transform: translateX(5px);
}

.footer-links a i {
    font-size: 0.8rem;
    width: 16px;
}

/* Contact Info */
.contact-info {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.contact-item i {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    margin-top: 2px;
    flex-shrink: 0;
}

.contact-item div span {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 3px;
}

.contact-item div a,
.contact-item div p {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 0.9rem;
    margin: 0;
    transition: var(--transition);
}

.contact-item div a:hover {
    color: var(--warning-color);
}

/* Social Media */
.social-media {
    margin-bottom: 30px;
}

.social-media h6 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.social-links {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: var(--transition);
}

.social-link:hover::before {
    transform: translateX(100%);
}

.social-link.facebook {
    background: #3b5998;
    color: white;
}

.social-link.twitter {
    background: #1da1f2;
    color: white;
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
}

.social-link.linkedin {
    background: #0077b5;
    color: white;
}

.social-link.youtube {
    background: #ff0000;
    color: white;
}

.social-link.telegram {
    background: #0088cc;
    color: white;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Admin Access */
.admin-access {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-admin {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: white;
    transition: var(--transition);
    text-align: center;
}

.btn-admin i {
    font-size: 2rem;
    color: var(--warning-color);
    margin-bottom: 8px;
}

.btn-admin span {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 3px;
}

.btn-admin small {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.btn-admin:hover {
    color: var(--warning-color);
    transform: translateY(-3px);
}

/* Footer Bottom */
.copyright {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--warning-color);
}

/* Floating Elements */
.footer-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.03);
    animation: float 20s infinite ease-in-out;
}

.floating-shape.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 5%;
    animation-delay: 0s;
}

.floating-shape.shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 10%;
    animation-delay: 7s;
}

.floating-shape.shape-3 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 70%;
    animation-delay: 14s;
}

/* Print Styles */
@media print {
    .navbar,
    .carousel-control-prev,
    .carousel-control-next,
    .btn,
    footer {
        display: none !important;
    }

    .hero-slide {
        min-height: auto;
        padding: 20px 0;
    }

    .course-card,
    .feature-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* Responsive Footer */
@media (max-width: 768px) {
    .admin-access {
        margin-top: 30px;
    }

    .btn-admin-footer {
        font-size: 0.9rem;
        padding: 10px 15px;
    }

    footer .col-md-3 {
        margin-bottom: 30px;
    }
}

/* New Hero Slider Styles */
.hero-content {
    color: white;
    z-index: 10;
    position: relative;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-badge i {
    color: var(--warning-color);
}

/* Hero Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Description */
.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--warning-color);
    line-height: 1;
}

.hero-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}

/* Hero Features */
.hero-features {
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 1rem;
}

.feature-item i {
    color: var(--success-color);
    font-size: 1.1rem;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.btn-hero {
    background: var(--warning-color);
    color: #333;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.btn-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: var(--transition);
}

.btn-hero:hover::before {
    left: 100%;
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.4);
    color: #333;
}

.btn-play {
    display: flex;
    align-items: center;
    gap: 15px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.btn-play:hover {
    color: var(--warning-color);
}

.play-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-play:hover .play-icon {
    background: var(--warning-color);
    color: #333;
    transform: scale(1.1);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 25px;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    color: white;
    transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    z-index: 10;
}

.main-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.main-image-container:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.02);
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition);
}

.main-image-container:hover .main-image {
    transform: scale(1.05);
}

/* Image Decorations */
.image-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.decoration {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 12px 16px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite;
}

.decoration-1 {
    top: 20px;
    right: 20px;
    animation-delay: 0s;
}

.decoration-2 {
    bottom: 80px;
    left: 20px;
    animation-delay: 2s;
}

.decoration-3 {
    top: 50%;
    right: -10px;
    animation-delay: 4s;
}

/* New Floating Cards */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    animation: float 8s ease-in-out infinite;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-1 {
    top: 10%;
    left: -20px;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: -30px;
    animation-delay: 3s;
}

.card-3 {
    bottom: 20%;
    left: -10px;
    animation-delay: 6s;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.card-content h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
}

.card-content p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Certificates Showcase */
.certificates-showcase {
    position: relative;
    height: 400px;
}

.certificate-card {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    width: 250px;
    animation: float 10s ease-in-out infinite;
    border: 2px solid var(--primary-color);
}

.cert-1 {
    top: 20px;
    left: 20px;
    animation-delay: 0s;
}

.cert-2 {
    top: 100px;
    right: 20px;
    animation-delay: 3s;
}

.cert-3 {
    bottom: 20px;
    left: 50px;
    animation-delay: 6s;
}

.cert-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.cert-header i {
    color: var(--warning-color);
    font-size: 1.2rem;
}

.cert-header span {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-muted);
}

.cert-body h6 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-dark);
}

.cert-body p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Top Bar Styles */
.top-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 0;
    font-size: 0.85rem;
    position: relative;
    z-index: 1051;
}

.contact-info {
    display: flex;
    gap: 25px;
    align-items: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.contact-item:hover {
    color: var(--warning-color);
}

.contact-item i {
    font-size: 0.8rem;
}

.top-social-links {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 15px;
}

.follow-text {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    margin-right: 10px;
}

.top-social-link {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 0.8rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.top-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: scale(0);
    transition: var(--transition);
}

.top-social-link:hover::before {
    transform: scale(1);
}

.top-social-link:hover {
    transform: translateY(-2px);
    color: white;
}

.admin-link {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    text-decoration: none;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-link:hover {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    transform: translateY(-1px);
}

/* Top Bar Toggle Button */
.top-bar-toggle {
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 25px;
    height: 25px;
    background: rgba(102, 126, 234, 0.8);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 0.7rem;
    cursor: pointer;
    z-index: 1060;
    transition: var(--transition);
    opacity: 0.6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.top-bar-toggle:hover {
    opacity: 1;
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.top-bar-toggle.hidden {
    transform: translateY(-50%) rotate(180deg);
}

/* Top Bar Animation */
.top-bar {
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.top-bar.hidden {
    transform: translateY(-100%);
    opacity: 0;
    pointer-events: none;
}

/* Adjust navbar position when top bar is hidden */
.premium-navbar {
    top: 32px;
    transition: top 0.3s ease;
}

.premium-navbar.top-bar-hidden {
    top: 0;
}

/* Premium Navbar Styles */
.premium-navbar {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(25px);
    box-shadow: 0 4px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 1050;
    top: 44px;
}

.premium-navbar.navbar-scrolled {
    background: rgba(255, 255, 255, 0.99) !important;
    padding: 10px 0;
    box-shadow: 0 6px 50px rgba(0, 0, 0, 0.12);
    top: 0;
}

/* Premium Brand */
.premium-brand {
    text-decoration: none;
    transition: var(--transition);
}

.premium-brand:hover {
    transform: scale(1.02);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-wrapper {
    position: relative;
}

.logo-icon {
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    animation: logoShimmer 3s ease-in-out infinite;
}

.logo-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 18px;
    z-index: -1;
    opacity: 0;
    transition: var(--transition);
}

.premium-brand:hover .logo-glow {
    opacity: 0.7;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes logoShimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.brand-content {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.brand-title {
    font-size: 1.6rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-tagline {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-muted);
    margin-top: 2px;
}

/* Premium Toggler */
.premium-toggler {
    border: none;
    padding: 8px;
    background: transparent;
    position: relative;
    width: 45px;
    height: 45px;
    border-radius: 12px;
    transition: var(--transition);
}

.premium-toggler:focus {
    box-shadow: none;
}

.premium-toggler:hover {
    background: rgba(102, 126, 234, 0.1);
}

.toggler-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 25px;
    height: 20px;
}

.toggler-icon span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--primary-color);
    margin: 2px 0;
    border-radius: 2px;
    transition: var(--transition);
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(2) {
    opacity: 0;
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Premium Navigation Links */
.navbar-nav .nav-link {
    position: relative;
    padding: 12px 20px !important;
    margin: 0 5px;
    border-radius: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-dark) !important;
    font-weight: 500;
    overflow: hidden;
}

.nav-icon {
    font-size: 1.1rem;
    transition: var(--transition);
}

.nav-text {
    font-size: 0.95rem;
    transition: var(--transition);
}

.nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-2px);
}

.navbar-nav .nav-link:hover .nav-indicator,
.navbar-nav .nav-link.active .nav-indicator {
    width: 30px;
}

.navbar-nav .nav-link:hover .nav-icon {
    transform: scale(1.1);
}

/* Mega Dropdown */
.mega-dropdown .dropdown-menu {
    position: static;
}

.mega-menu {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    padding: 30px;
    margin-top: 15px;
    width: 100%;
    left: 0 !important;
    transform: none !important;
}

.mega-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.mega-link {
    display: block;
    color: var(--text-dark);
    text-decoration: none;
    padding: 8px 0;
    font-size: 0.9rem;
    transition: var(--transition);
    position: relative;
    padding-left: 15px;
}

.mega-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%) scale(0);
    transition: var(--transition);
}

.mega-link:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.mega-link:hover::before {
    transform: translateY(-50%) scale(1);
}

.mega-featured {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 15px;
    padding: 20px;
    text-align: center;
}

.featured-course img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.featured-course h6 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.featured-course p {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-bottom: 15px;
}

/* Navbar End Section */
.navbar-end {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Premium Search */
.search-container {
    position: relative;
}

.search-box-premium {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-premium {
    background: rgba(102, 126, 234, 0.08);
    border: 2px solid transparent;
    border-radius: 25px;
    padding: 10px 45px 10px 20px;
    font-size: 0.9rem;
    width: 250px;
    transition: all 0.3s ease;
    color: var(--text-dark);
}

.search-input-premium::placeholder {
    color: var(--text-muted);
}

.search-input-premium:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    width: 280px;
}

.search-btn-premium {
    position: absolute;
    right: 5px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
    font-size: 0.9rem;
}

.search-btn-premium:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* Auth Section */
.auth-section {
    display: flex;
    gap: 12px;
}

.btn-auth {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-auth::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: var(--transition);
}

.btn-auth:hover::before {
    left: 100%;
}

.btn-login {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-login:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-register {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-register:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
}

/* Premium Footer Styles */
.premium-footer {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
    color: white;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
    padding: 60px 0;
    position: relative;
    z-index: 2;
}

.newsletter-content {
    position: relative;
    z-index: 2;
}

.newsletter-text h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
}

.newsletter-text p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

.subscription-form {
    max-width: 500px;
    margin-left: auto;
}

.input-group {
    display: flex;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 50px;
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    color: white;
    font-size: 1rem;
    border-radius: 50px;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
    outline: none;
}

.newsletter-btn {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
    white-space: nowrap;
}

.newsletter-btn:hover {
    background: var(--warning-color);
    color: #333;
    transform: scale(1.05);
}

/* Main Footer */
.footer-main {
    padding: 80px 0 40px;
    position: relative;
    z-index: 2;
}

/* Footer Brand */
.footer-brand {
    margin-bottom: 30px;
}

.brand-logo-footer {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.logo-icon-footer {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.logo-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 25px;
    z-index: -1;
    opacity: 0.7;
    animation: pulse 3s ease-in-out infinite;
}

.brand-info h3 {
    font-size: 1.8rem;
    font-weight: 800;
    margin: 0 0 5px 0;
    color: white;
}

.brand-slogan {
    font-size: 1rem;
    color: var(--warning-color);
    font-weight: 500;
}

.company-description {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
}

/* Achievement Stats */
.achievement-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.achievement-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.achievement-icon {
    width: 50px;
    height: 50px;
    background: var(--warning-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #333;
}

.achievement-info {
    display: flex;
    flex-direction: column;
}

.achievement-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.achievement-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 3px;
}

/* Section Titles */
.section-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: white;
    position: relative;
    padding-bottom: 12px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--warning-color);
    border-radius: 2px;
}

/* Footer Menu */
.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li {
    margin-bottom: 12px;
}

.footer-menu a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.95rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
}

.footer-menu a::before {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--warning-color);
    border-radius: 50%;
    transform: scale(0);
    transition: var(--transition);
}

.footer-menu a:hover {
    color: var(--warning-color);
    transform: translateX(8px);
}

.footer-menu a:hover::before {
    transform: scale(1);
}

/* Contact Details */
.contact-details {
    margin-bottom: 35px;
}

.contact-detail-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: var(--transition);
}

.contact-detail-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.contact-icon {
    width: 45px;
    height: 45px;
    background: var(--primary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
}

.contact-text h6 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: white;
}

.contact-text p {
    margin: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

.contact-text a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.contact-text a:hover {
    color: var(--warning-color);
}

/* Social Section */
.social-section {
    margin-top: 30px;
}

.social-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.social-platforms {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-platform {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-platform:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.platform-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
}

.social-platform.facebook .platform-icon {
    background: #1877f2;
}

.social-platform.twitter .platform-icon {
    background: #1da1f2;
}

.social-platform.instagram .platform-icon {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-platform.youtube .platform-icon {
    background: #ff0000;
}

.social-platform.linkedin .platform-icon {
    background: #0077b5;
}

.platform-info {
    display: flex;
    flex-direction: column;
}

.platform-name {
    font-size: 0.95rem;
    font-weight: 600;
    color: white;
    line-height: 1;
}

.platform-followers {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 3px;
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
}

.copyright-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer-links-bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 25px;
    flex-wrap: wrap;
}

.footer-links-bottom a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
    padding: 5px 0;
}

.footer-links-bottom a:hover {
    color: var(--warning-color);
}

.admin-link-bottom {
    background: rgba(255, 193, 7, 0.15);
    color: var(--warning-color) !important;
    padding: 8px 15px !important;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.admin-link-bottom:hover {
    background: rgba(255, 193, 7, 0.25);
    color: var(--warning-color) !important;
    transform: translateY(-2px);
}

/* Background Elements */
.footer-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></svg>') repeat;
    animation: patternMove 60s linear infinite;
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: particleFloat 20s infinite ease-in-out;
}

.particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 5s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: 10s;
}

.particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 15s;
}

.particle:nth-child(5) {
    top: 70%;
    left: 10%;
    animation-delay: 20s;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.2);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-40px) translateX(-10px) scale(0.8);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-20px) translateX(15px) scale(1.1);
        opacity: 0.4;
    }
}

/* Brand Logo */
.modern-brand {
    text-decoration: none;
    transition: var(--transition);
}

.modern-brand:hover {
    transform: scale(1.05);
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.modern-brand:hover .logo-icon::before {
    opacity: 1;
    animation: shimmer 1s ease-in-out;
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.brand-subtitle {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-muted);
}

/* Custom Toggler */
.custom-toggler {
    border: none;
    padding: 8px;
    background: transparent;
    position: relative;
    width: 40px;
    height: 40px;
}

.toggler-line {
    display: block;
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 5px 0;
    border-radius: 2px;
    transition: var(--transition);
}

.custom-toggler:focus {
    box-shadow: none;
}

.custom-toggler[aria-expanded="true"] .toggler-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.custom-toggler[aria-expanded="true"] .toggler-line:nth-child(2) {
    opacity: 0;
}

.custom-toggler[aria-expanded="true"] .toggler-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation Links */
.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    margin: 0 8px;
    padding: 12px 16px !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link i {
    font-size: 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1);
}

/* Custom Dropdown */
.custom-dropdown {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    padding: 10px;
    margin-top: 10px;
}

.custom-dropdown .dropdown-item {
    border-radius: 10px;
    padding: 10px 15px;
    transition: var(--transition);
    color: var(--text-dark);
}

.custom-dropdown .dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* Navbar Actions */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Search Box */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid transparent;
    border-radius: 25px;
    padding: 6px 35px 6px 12px;
    font-size: 0.85rem;
    width: 180px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.search-btn {
    position: absolute;
    right: 3px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
    font-size: 0.8rem;
}

.search-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

/* Auth Buttons */
.auth-buttons {
    display: flex;
    gap: 8px;
}

.auth-buttons .btn {
    border-radius: 20px;
    padding: 6px 15px;
    font-weight: 500;
    font-size: 0.85rem;
    transition: var(--transition);
}

.auth-buttons .btn:hover {
    transform: translateY(-2px);
}

/* Social Links in Navbar */
.social-links-nav {
    display: flex;
    gap: 8px;
}

.social-link {
    width: 32px;
    height: 32px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.85rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design for New Elements */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .floating-card {
        display: none;
    }
}

@media (max-width: 992px) {
    .custom-indicators {
        bottom: 20px;
        gap: 15px;
    }

    .custom-indicators button {
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .btn-hero,
    .btn-play {
        justify-content: center;
    }

    .navbar-actions {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
    }

    .search-box {
        order: 1;
    }

    .auth-buttons {
        order: 2;
        justify-content: center;
    }

    .social-links-nav {
        order: 3;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .hero-stats .stat-item {
        text-align: center;
    }

    .hero-features {
        margin-bottom: 2rem;
    }

    .custom-indicators {
        flex-direction: column;
        gap: 10px;
        bottom: 10px;
        left: 10px;
        transform: none;
    }

    .custom-indicators button {
        padding: 6px 12px;
        font-size: 0.7rem;
    }

    .indicator-text {
        display: none;
    }

    .main-image-container {
        margin-top: 30px;
        transform: none;
    }

    .certificates-showcase {
        height: 300px;
    }

    .certificate-card {
        width: 200px;
        padding: 15px;
    }

    .brand-logo {
        gap: 8px;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .brand-name {
        font-size: 1.1rem;
    }

    .brand-subtitle {
        font-size: 0.8rem;
    }

    .search-input {
        width: 100%;
    }

    .auth-buttons {
        width: 100%;
    }

    .auth-buttons .btn {
        flex: 1;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .btn-hero {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .play-icon {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .decoration {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .certificate-card {
        width: 180px;
        padding: 12px;
    }

    .cert-header {
        margin-bottom: 10px;
    }

    .cert-body h6 {
        font-size: 0.9rem;
    }

    .cert-body p {
        font-size: 0.7rem;
    }
}

/* Footer Responsive */
@media (max-width: 992px) {
    .footer-top {
        padding: 60px 0 30px;
    }

    .footer-stats {
        justify-content: center;
        gap: 20px;
    }

    .social-links {
        justify-content: center;
    }

    .footer-bottom-links {
        justify-content: center;
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .footer-top {
        padding: 50px 0 25px;
    }

    .footer-logo {
        justify-content: center;
        text-align: center;
    }

    .footer-stats {
        gap: 15px;
    }

    .footer-stats .number {
        font-size: 1.3rem;
    }

    .widget-title {
        text-align: center;
        margin-bottom: 20px;
    }

    .widget-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links {
        text-align: center;
    }

    .contact-info {
        text-align: center;
    }

    .contact-item {
        justify-content: center;
        text-align: left;
    }

    .social-media {
        text-align: center;
    }

    .admin-access {
        text-align: center;
        margin-top: 20px;
    }

    .footer-bottom-links {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .copyright {
        text-align: center;
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .footer-logo .logo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .footer-logo .logo-text h4 {
        font-size: 1.2rem;
    }

    .footer-stats {
        flex-direction: column;
        gap: 10px;
    }

    .social-links {
        gap: 8px;
    }

    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .floating-shape {
        display: none;
    }
}

/* ===== PREMIUM HEADER & FOOTER REDESIGN ===== */

/* Enhanced Top Bar Styles */
.top-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    padding: 6px 0;
    font-size: 0.75rem;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
        linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.05) 50%, transparent 100%);
    animation: shimmerTopBar 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmerTopBar {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.contact-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.95);
    transition: var(--transition);
    padding: 3px 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.7rem;
}

.contact-item:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.contact-item i {
    font-size: 0.7rem;
    padding: 2px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.top-social-links {
    display: flex;
    gap: 8px;
    align-items: center;
}

.follow-text {
    font-size: 0.65rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    margin-right: 6px;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.top-social-link {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 0.7rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.top-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: translateX(-100%);
    transition: var(--transition);
}

.top-social-link:hover::before {
    transform: translateX(100%);
}

.top-social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.top-social-link.facebook:hover {
    background: #3b5998;
    border-color: #3b5998;
}
.top-social-link.twitter:hover {
    background: #1da1f2;
    border-color: #1da1f2;
}
.top-social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743);
    border-color: #f09433;
}
.top-social-link.linkedin:hover {
    background: #0077b5;
    border-color: #0077b5;
}
.top-social-link.youtube:hover {
    background: #ff0000;
    border-color: #ff0000;
}

.admin-link {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    text-decoration: none;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.65rem;
    font-weight: 600;
    transition: var(--transition);
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 1px 4px rgba(255, 215, 0, 0.3);
}

.admin-link:hover {
    background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
    color: #333;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.5);
}

.admin-link i {
    margin-right: 4px;
    font-size: 0.7rem;
}

/* Premium Brand */
.premium-brand {
    text-decoration: none;
    transition: var(--transition);
}

.premium-brand:hover {
    transform: scale(1.02);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-wrapper {
    position: relative;
}

.logo-icon {
    width: 55px;
    height: 55px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.6rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.brand-content {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-tagline {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-muted);
}

/* Premium Toggler */
.premium-toggler {
    border: none;
    padding: 8px;
    background: transparent;
    position: relative;
    width: 40px;
    height: 40px;
}

.toggler-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.toggler-icon span {
    display: block;
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    border-radius: 2px;
    transition: var(--transition);
}

.premium-toggler:focus {
    box-shadow: none;
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(2) {
    opacity: 0;
}

.premium-toggler[aria-expanded="true"] .toggler-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation Links */
.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    margin: 0 8px;
    padding: 12px 16px !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    overflow: hidden;
}

.nav-icon {
    font-size: 1rem;
    transition: var(--transition);
}

.nav-text {
    font-weight: 500;
}

.nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link:hover .nav-indicator,
.navbar-nav .nav-link.active .nav-indicator {
    width: 80%;
}

.navbar-nav .nav-link:hover .nav-icon {
    transform: scale(1.1);
}

/* Mega Dropdown */
.mega-dropdown .dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    padding: 30px;
    margin-top: 15px;
    min-width: 600px;
}

.mega-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.mega-link {
    display: block;
    padding: 8px 0;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.mega-link:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.mega-featured {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    padding: 20px;
}

.featured-course img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.featured-course h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.featured-course p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 10px;
}

/* Navbar End Actions */
.navbar-end {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box-premium {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-premium {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid transparent;
    border-radius: 25px;
    padding: 8px 40px 8px 15px;
    font-size: 0.9rem;
    width: 220px;
    transition: var(--transition);
}

.search-input-premium:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.search-btn-premium {
    position: absolute;
    right: 5px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
}

.search-btn-premium:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

/* Auth Buttons */
.auth-section {
    display: flex;
    gap: 10px;
}

.btn-auth {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
}

.btn-login {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-login:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-register {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-register:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Responsive Design for Premium Header */
@media (max-width: 992px) {
    .top-bar {
        padding: 6px 0;
        font-size: 0.8rem;
    }

    .contact-info {
        gap: 15px;
    }

    .top-social {
        gap: 8px;
    }

    .social-icon {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }

    .premium-navbar {
        padding: 10px 0;
    }

    .brand-container {
        gap: 10px;
    }

    .logo-icon {
        width: 45px;
        height: 45px;
        font-size: 1.4rem;
    }

    .brand-title {
        font-size: 1.2rem;
    }

    .brand-tagline {
        font-size: 0.8rem;
    }

    .search-input-premium {
        width: 180px;
    }

    .navbar-end {
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        display: none;
    }

    .premium-navbar {
        top: 0 !important;
        padding: 8px 0;
    }

    .brand-container {
        gap: 8px;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .brand-title {
        font-size: 1.1rem;
    }

    .brand-tagline {
        font-size: 0.75rem;
    }

    .navbar-end {
        flex-direction: column;
        gap: 10px;
        margin-top: 15px;
    }

    .search-container {
        order: 1;
        width: 100%;
    }

    .search-input-premium {
        width: 100%;
    }

    .auth-section {
        order: 2;
        width: 100%;
        justify-content: center;
    }

    .btn-auth {
        flex: 1;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .contact-info {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .top-social {
        justify-content: center;
    }

    .brand-title {
        font-size: 1rem;
    }

    .brand-tagline {
        font-size: 0.7rem;
    }

    .mega-dropdown .dropdown-menu {
        min-width: 300px;
        padding: 20px;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    font-family: inherit;
    max-width: 350px;
}

.notification.notification-success {
    background: #28a745;
}

.notification.notification-error {
    background: #dc3545;
}

.notification.notification-warning {
    background: #ffc107;
    color: #333;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification.show {
    transform: translateX(0);
}

/* Smooth Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Header & Footer Responsive Design */
@media (max-width: 1200px) {
    .search-input-premium {
        width: 200px;
    }

    .search-input-premium:focus {
        width: 220px;
    }

    .achievement-stats {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 15px;
    }

    .achievement-item {
        flex: 1;
        min-width: 200px;
    }
}

@media (max-width: 992px) {
    .top-bar {
        padding: 6px 0;
        font-size: 0.8rem;
    }

    .contact-info {
        gap: 15px;
    }

    .top-social-links {
        gap: 10px;
    }

    .premium-navbar {
        padding: 12px 0;
    }

    .brand-title {
        font-size: 1.4rem;
    }

    .brand-tagline {
        font-size: 0.8rem;
    }

    .navbar-end {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
    }

    .search-container {
        order: 1;
        width: 100%;
    }

    .search-input-premium {
        width: 100%;
    }

    .auth-section {
        order: 2;
        justify-content: center;
        width: 100%;
    }

    .newsletter-text h3 {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .newsletter-text p {
        text-align: center;
        margin-bottom: 30px;
    }

    .achievement-stats {
        flex-direction: column;
        gap: 15px;
    }

    .social-platforms {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .footer-links-bottom {
        justify-content: center;
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        text-align: center;
    }

    .contact-info {
        justify-content: center;
        margin-bottom: 15px;
    }

    .top-social-links {
        justify-content: center;
    }

    .follow-text {
        margin-right: 0;
        margin-bottom: 10px;
        display: block;
        width: 100%;
        text-align: center;
    }

    .brand-container {
        gap: 10px;
    }

    .logo-icon-footer {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }

    .brand-info h3 {
        font-size: 1.5rem;
    }

    .mega-menu {
        padding: 20px;
    }

    .mega-menu .row {
        flex-direction: column;
    }

    .mega-menu .col-md-3 {
        margin-bottom: 20px;
    }

    .newsletter-section {
        padding: 40px 0;
    }

    .newsletter-text h3 {
        font-size: 1.5rem;
    }

    .input-group {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
        border-radius: 20px;
    }

    .newsletter-input {
        text-align: center;
    }

    .newsletter-btn {
        justify-content: center;
        border-radius: 25px;
    }

    .footer-main {
        padding: 50px 0 30px;
    }

    .section-title {
        text-align: center;
        font-size: 1.2rem;
    }

    .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-menu {
        text-align: center;
    }

    .contact-details {
        margin-bottom: 25px;
    }

    .social-platforms {
        grid-template-columns: 1fr;
    }

    .footer-links-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .copyright-text {
        text-align: center;
        margin-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .top-bar {
        padding: 5px 0;
    }

    .contact-info {
        flex-direction: column;
        gap: 8px;
    }

    .contact-item {
        font-size: 0.75rem;
    }

    .top-social-links {
        gap: 8px;
    }

    .top-social-link {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }

    .admin-link {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .logo-icon {
        width: 45px;
        height: 45px;
        font-size: 1.5rem;
    }

    .brand-title {
        font-size: 1.2rem;
    }

    .brand-tagline {
        font-size: 0.75rem;
    }

    .nav-text {
        font-size: 0.85rem;
    }

    .btn-auth {
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .newsletter-text h3 {
        font-size: 1.3rem;
    }

    .newsletter-text p {
        font-size: 0.9rem;
    }

    .brand-logo-footer {
        justify-content: center;
        text-align: center;
    }

    .logo-icon-footer {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .brand-info h3 {
        font-size: 1.3rem;
    }

    .achievement-item {
        padding: 12px;
    }

    .achievement-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .achievement-number {
        font-size: 1.2rem;
    }

    .contact-detail-item {
        padding: 12px;
    }

    .contact-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .platform-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .platform-name {
        font-size: 0.85rem;
    }

    .platform-followers {
        font-size: 0.75rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.border-radius-custom {
    border-radius: 15px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .feature-card,
    .course-card,
    .quiz-container,
    .forum-post,
    .dashboard-card {
        background: #2d2d2d;
        color: #e0e0e0;
    }
}
