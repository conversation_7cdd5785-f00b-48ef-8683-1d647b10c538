/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-image img {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
    background: rgba(66, 133, 244, 0.1);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

/* Course Cards */
.course-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.course-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.course-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
}

.course-content {
    padding: 20px;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.rating {
    color: #ffc107;
}

/* Statistics */
.stat-item {
    padding: 20px;
}

.stat-item h3 {
    margin-bottom: 10px;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
}

.btn-warning {
    background: #ffc107;
    border: none;
    color: #333;
}

.btn-warning:hover {
    background: #e0a800;
    color: #333;
    transform: translateY(-2px);
}

/* Forms */
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 40px;
    margin-top: 100px;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Video Player */
.video-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 400px;
}

.video-controls {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 5px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    border-radius: 3px;
    transition: width 0.1s ease;
}

/* Quiz Styles */
.quiz-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.question {
    margin-bottom: 30px;
}

.question h4 {
    margin-bottom: 20px;
    color: #333;
}

.answer-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-option:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.answer-option.selected {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

.answer-option.correct {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

.answer-option.incorrect {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

/* Forum Styles */
.forum-post {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-left: 15px;
}

.post-meta {
    color: #666;
    font-size: 0.9rem;
}

/* Certificate Styles */
.certificate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.certificate::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.certificate-content {
    position: relative;
    z-index: 1;
}

/* Dashboard Styles */
.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.dashboard-stat {
    text-align: center;
    padding: 20px;
}

.dashboard-stat i {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 100px 0 50px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .feature-card,
    .course-card {
        margin-bottom: 30px;
    }
    
    .form-container {
        margin: 80px 15px 20px;
        padding: 30px 20px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.border-radius-custom {
    border-radius: 15px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .feature-card,
    .course-card,
    .quiz-container,
    .forum-post,
    .dashboard-card {
        background: #2d2d2d;
        color: #e0e0e0;
    }
}
