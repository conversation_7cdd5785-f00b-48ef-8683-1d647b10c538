/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
    --shadow-medium: 0 10px 30px rgba(0,0,0,0.12);
    --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Se<PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    transition: var(--transition);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 15px;
    padding: 8px 16px !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--warning-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
    background: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    color: var(--warning-color) !important;
    background: rgba(255, 255, 255, 0.15);
}

/* Hero Slider Section */
.hero-slider {
    position: relative;
    overflow: hidden;
}

.hero-slide {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    position: relative;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.hero-image:hover img {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: float-up-down 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.floating-card span {
    font-weight: 600;
    color: var(--dark-color);
}

.card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 5%;
    animation-delay: 1s;
}

.card-3 {
    top: 40%;
    left: 10%;
    animation-delay: 2s;
}

/* Hero Stats */
.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-mini h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-mini small {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Hero Badge */
.hero-badge .badge {
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 25px;
    padding: 8px 20px;
    box-shadow: var(--shadow-light);
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.8;
    transition: var(--transition);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background: white;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-image: none;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.carousel-indicators {
    bottom: 30px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid white;
    transition: var(--transition);
}

.carousel-indicators button.active {
    background: white;
    transform: scale(1.2);
}

/* Section Titles */
.section-title {
    position: relative;
    margin-bottom: 1rem;
}

.title-underline {
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
    margin-top: 1rem;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.feature-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--gradient-primary);
    border-radius: 50%;
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.feature-card:hover .feature-icon::before {
    width: 100%;
    height: 100%;
}

.feature-icon i {
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.feature-card:hover .feature-icon i {
    color: white;
}

/* Courses Section */
.courses-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.courses-slider {
    position: relative;
    padding: 0 60px;
}

.courses-control-prev,
.courses-control-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    z-index: 10;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.courses-control-prev {
    right: 10px;
}

.courses-control-next {
    left: 10px;
}

.courses-control-prev:hover,
.courses-control-next:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-medium);
}

/* Course Cards */
.course-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
    margin: 0 10px;
    position: relative;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.course-card:hover::before {
    opacity: 0.05;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.course-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.course-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    opacity: 0;
    transition: var(--transition);
}

.course-card:hover .course-image::before {
    opacity: 1;
}

.course-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: var(--transition);
    z-index: 3;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-category {
    margin-bottom: 15px;
}

.course-category .badge {
    margin-left: 5px;
    font-size: 0.75rem;
    padding: 5px 10px;
}

.course-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    line-height: 1.4;
}

.course-description {
    font-size: 0.9rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-instructor img {
    width: 35px;
    height: 35px;
    object-fit: cover;
}

.course-stats {
    background: var(--light-color);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 15px 0;
}

.course-stats .col-4 {
    border-left: 1px solid #dee2e6;
}

.course-stats .col-4:last-child {
    border-left: none;
}

.course-actions .btn {
    font-weight: 600;
    padding: 12px 20px;
}

.course-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: var(--shadow-light);
    z-index: 2;
}

.course-content {
    padding: 25px;
    position: relative;
    z-index: 2;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.rating {
    color: var(--warning-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Instructors Section */
.instructors-section {
    background: white;
}

.instructor-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    text-align: center;
    position: relative;
}

.instructor-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.instructor-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.instructor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.instructor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.instructor-card:hover .instructor-overlay {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: white;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    transform: translateY(20px);
}

.instructor-card:hover .social-links a {
    transform: translateY(0);
}

.social-links a:hover {
    background: var(--warning-color);
    color: white;
    transform: scale(1.1);
}

.instructor-info {
    padding: 25px;
}

.instructor-info h5 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.instructor-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    font-size: 0.9rem;
}

.instructor-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
}

/* Statistics Section */
.stats-section {
    background: var(--gradient-primary);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 30s infinite linear;
}

.stat-item {
    padding: 30px 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    transition: var(--transition);
}

.stat-item:hover .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.stat-item h3 {
    margin-bottom: 10px;
    font-weight: 700;
}

.stat-item p {
    margin: 0;
    opacity: 0.9;
}

/* Modern Buttons */
.btn {
    border-radius: 50px;
    padding: 14px 35px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    border: none;
    color: #333;
    box-shadow: var(--shadow-light);
}

.btn-warning:hover {
    background: #e0a800;
    color: #333;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-outline-light {
    border: 2px solid rgba(255,255,255,0.8);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition);
}

.modern-btn:hover::after {
    width: 300px;
    height: 300px;
}

.pulse-btn {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* Forms */
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 40px;
    margin-top: 100px;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Video Player */
.video-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 400px;
}

.video-controls {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 5px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    border-radius: 3px;
    transition: width 0.1s ease;
}

/* Quiz Styles */
.quiz-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.question {
    margin-bottom: 30px;
}

.question h4 {
    margin-bottom: 20px;
    color: #333;
}

.answer-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-option:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.answer-option.selected {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

.answer-option.correct {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

.answer-option.incorrect {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

/* Forum Styles */
.forum-post {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-left: 15px;
}

.post-meta {
    color: #666;
    font-size: 0.9rem;
}

/* Certificate Styles */
.certificate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.certificate::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.certificate-content {
    position: relative;
    z-index: 1;
}

/* Dashboard Styles */
.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.dashboard-stat {
    text-align: center;
    padding: 20px;
}

.dashboard-stat i {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .courses-slider {
        padding: 0 40px;
    }

    .floating-elements {
        display: none;
    }
}

@media (max-width: 992px) {
    .hero-slide {
        text-align: center;
        padding: 120px 0 60px;
    }

    .hero-slide h1 {
        font-size: 2.5rem;
    }

    .courses-slider {
        padding: 0 20px;
    }

    .courses-control-prev,
    .courses-control-next {
        display: none;
    }

    .instructor-card {
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .hero-slide {
        min-height: 80vh;
        padding: 100px 0 40px;
    }

    .hero-slide h1 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .hero-slide .lead {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .hero-slide .btn {
        padding: 12px 25px;
        font-size: 0.9rem;
        margin-bottom: 10px;
        width: 100%;
    }

    .feature-card,
    .course-card {
        margin-bottom: 30px;
    }

    .form-container {
        margin: 80px 15px 20px;
        padding: 30px 20px;
    }

    .stat-item {
        margin-bottom: 30px;
    }

    .stat-item h3 {
        font-size: 2rem;
    }

    .instructor-image {
        height: 200px;
    }

    .course-stats {
        padding: 10px 5px;
    }

    .course-stats .col-4 {
        font-size: 0.8rem;
    }

    .navbar-nav .nav-link {
        margin: 5px 0;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .hero-slide h1 {
        font-size: 1.75rem;
    }

    .display-5 {
        font-size: 1.5rem;
    }

    .courses-slider {
        padding: 0 10px;
    }

    .course-card {
        margin: 0 5px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .instructor-info {
        padding: 20px 15px;
    }

    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes float-up-down {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0,-30px,0);
    }
    70% {
        transform: translate3d(0,-15px,0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Parallax Effect */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.parallax-element {
    will-change: transform;
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Lazy Loading */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Enhanced Hover Effects */
.enhanced-hover {
    position: relative;
    overflow: hidden;
}

.enhanced-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.enhanced-hover:hover::before {
    left: 100%;
}

/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform-origin: left;
    transform: scaleX(0);
    z-index: 9999;
    transition: transform 0.1s ease-out;
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    border-color: var(--primary-color);
}

/* Print Styles */
@media print {
    .navbar,
    .carousel-control-prev,
    .carousel-control-next,
    .btn,
    footer {
        display: none !important;
    }

    .hero-slide {
        min-height: auto;
        padding: 20px 0;
    }

    .course-card,
    .feature-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.border-radius-custom {
    border-radius: 15px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .feature-card,
    .course-card,
    .quiz-container,
    .forum-post,
    .dashboard-card {
        background: #2d2d2d;
        color: #e0e0e0;
    }
}
