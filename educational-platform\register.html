<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - منصة التعلم الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعلم الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">العودة للرئيسية</a>
            </div>
        </div>
    </nav>

    <!-- Registration Form -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="form-container">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-plus fa-4x text-primary mb-3"></i>
                            <h2 class="fw-bold">إنشاء حساب جديد</h2>
                            <p class="text-muted">انضم إلى منصتنا وابدأ رحلة التعلم</p>
                        </div>

                        <form id="register-form">
                            <!-- Personal Information -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">الاسم الأول</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" class="form-control" id="firstName" required 
                                               placeholder="الاسم الأول">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">الاسم الأخير</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" class="form-control" id="lastName" required 
                                               placeholder="الاسم الأخير">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" required 
                                           placeholder="أدخل بريدك الإلكتروني">
                                </div>
                                <div class="form-text">سنستخدم هذا البريد للتواصل معك</div>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input type="tel" class="form-control" id="phone" 
                                           placeholder="05xxxxxxxx">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" required 
                                               placeholder="كلمة المرور">
                                        <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">يجب أن تحتوي على 8 أحرف على الأقل</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="confirmPassword" required 
                                               placeholder="تأكيد كلمة المرور">
                                    </div>
                                </div>
                            </div>

                            <!-- Password Strength Indicator -->
                            <div class="mb-3">
                                <div class="password-strength">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="password-strength-bar" 
                                             role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small id="password-strength-text" class="form-text"></small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="userType" class="form-label">نوع الحساب</label>
                                <select class="form-select" id="userType" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <option value="student">طالب</option>
                                    <option value="teacher">مدرس</option>
                                </select>
                            </div>

                            <!-- Additional fields for teachers -->
                            <div id="teacher-fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="specialty" class="form-label">التخصص</label>
                                    <select class="form-select" id="specialty">
                                        <option value="">اختر التخصص</option>
                                        <option value="البرمجة">البرمجة</option>
                                        <option value="التصميم">التصميم</option>
                                        <option value="التسويق">التسويق</option>
                                        <option value="الإدارة">الإدارة</option>
                                        <option value="التكنولوجيا">التكنولوجيا</option>
                                        <option value="اللغات">اللغات</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="experience" class="form-label">سنوات الخبرة</label>
                                    <select class="form-select" id="experience">
                                        <option value="">اختر سنوات الخبرة</option>
                                        <option value="1-2">1-2 سنة</option>
                                        <option value="3-5">3-5 سنوات</option>
                                        <option value="6-10">6-10 سنوات</option>
                                        <option value="10+">أكثر من 10 سنوات</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="bio" class="form-label">نبذة عنك</label>
                                    <textarea class="form-control" id="bio" rows="3" 
                                              placeholder="اكتب نبذة مختصرة عن خبرتك ومؤهلاتك"></textarea>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على 
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">شروط الاستخدام</a>
                                    و
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">سياسة الخصوصية</a>
                                </label>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    أريد الحصول على النشرة الإخبارية والعروض الخاصة
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    إنشاء الحساب
                                </button>
                            </div>

                            <hr class="my-4">

                            <div class="text-center">
                                <p class="mb-3">أو سجل باستخدام:</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-outline-primary flex-fill">
                                        <i class="fab fa-google me-2"></i>
                                        Google
                                    </button>
                                    <button type="button" class="btn btn-outline-info flex-fill">
                                        <i class="fab fa-facebook me-2"></i>
                                        Facebook
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="login.html" class="text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Terms Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">شروط الاستخدام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. قبول الشروط</h6>
                    <p>باستخدام هذه المنصة، فإنك توافق على الالتزام بهذه الشروط والأحكام.</p>
                    
                    <h6>2. استخدام المنصة</h6>
                    <p>يجب استخدام المنصة للأغراض التعليمية فقط وعدم انتهاك حقوق الآخرين.</p>
                    
                    <h6>3. المحتوى</h6>
                    <p>جميع المحتويات محمية بحقوق الطبع والنشر ولا يجوز نسخها أو توزيعها بدون إذن.</p>
                    
                    <h6>4. المسؤولية</h6>
                    <p>المنصة غير مسؤولة عن أي أضرار قد تنتج عن استخدام الخدمة.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Modal -->
    <div class="modal fade" id="privacyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سياسة الخصوصية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>جمع المعلومات</h6>
                    <p>نجمع المعلومات التي تقدمها عند التسجيل واستخدام المنصة.</p>
                    
                    <h6>استخدام المعلومات</h6>
                    <p>نستخدم معلوماتك لتحسين الخدمة وتخصيص المحتوى المناسب لك.</p>
                    
                    <h6>حماية المعلومات</h6>
                    <p>نتخذ إجراءات أمنية صارمة لحماية معلوماتك الشخصية.</p>
                    
                    <h6>مشاركة المعلومات</h6>
                    <p>لا نشارك معلوماتك الشخصية مع أطراف ثالثة بدون موافقتك.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data.js"></script>
    <script>
        // Registration form handling
        document.getElementById('register-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateForm()) {
                const userData = collectFormData();
                
                // Simulate registration
                registerUser(userData);
                
                showAlert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success');
                
                // Redirect to login page after delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        });
        
        function validateForm() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                showAlert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'danger');
                return false;
            }
            
            if (password.length < 8) {
                showAlert('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل', 'danger');
                return false;
            }
            
            return true;
        }
        
        function collectFormData() {
            return {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                userType: document.getElementById('userType').value,
                specialty: document.getElementById('specialty').value,
                experience: document.getElementById('experience').value,
                bio: document.getElementById('bio').value,
                newsletter: document.getElementById('newsletter').checked
            };
        }
        
        function registerUser(userData) {
            // Save to localStorage for demo purposes
            const users = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
            users.push({
                ...userData,
                id: Date.now(),
                registrationDate: new Date().toISOString(),
                enrolledCourses: [],
                completedCourses: [],
                certificates: []
            });
            localStorage.setItem('registeredUsers', JSON.stringify(users));
        }
        
        // Show/hide teacher fields based on user type
        document.getElementById('userType').addEventListener('change', function() {
            const teacherFields = document.getElementById('teacher-fields');
            if (this.value === 'teacher') {
                teacherFields.style.display = 'block';
                document.getElementById('specialty').required = true;
                document.getElementById('experience').required = true;
            } else {
                teacherFields.style.display = 'none';
                document.getElementById('specialty').required = false;
                document.getElementById('experience').required = false;
            }
        });
        
        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            updatePasswordStrengthIndicator(strength);
        });
        
        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score += 25;
            if (password.match(/[a-z]/)) score += 25;
            if (password.match(/[A-Z]/)) score += 25;
            if (password.match(/[0-9]/)) score += 25;
            if (password.match(/[^a-zA-Z0-9]/)) score += 25;
            
            return Math.min(score, 100);
        }
        
        function updatePasswordStrengthIndicator(strength) {
            const bar = document.getElementById('password-strength-bar');
            const text = document.getElementById('password-strength-text');
            
            bar.style.width = strength + '%';
            
            if (strength < 25) {
                bar.className = 'progress-bar bg-danger';
                text.textContent = 'ضعيفة جداً';
                text.className = 'form-text text-danger';
            } else if (strength < 50) {
                bar.className = 'progress-bar bg-warning';
                text.textContent = 'ضعيفة';
                text.className = 'form-text text-warning';
            } else if (strength < 75) {
                bar.className = 'progress-bar bg-info';
                text.textContent = 'متوسطة';
                text.className = 'form-text text-info';
            } else {
                bar.className = 'progress-bar bg-success';
                text.textContent = 'قوية';
                text.className = 'form-text text-success';
            }
        }
        
        // Toggle password visibility
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.form-container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
