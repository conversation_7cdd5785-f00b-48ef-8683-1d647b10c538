<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجمع المؤمل السكني</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="logo logo-left">
                <svg width="50" height="50" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="45" stroke="black" stroke-width="10"/>
                    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-size="40">L1</text>
                </svg>
            </div>
            <h1>مجمع المؤمل السكني</h1>
            <div class="logo logo-right">
                <svg width="50" height="50" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <rect width="100" height="100" rx="15" fill="#cccccc"/>
                     <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-size="40">L2</text>
                </svg>
            </div>
        </header>

        <main>
            
            <section class="customer-info-section bordered bordered-or">
                <h2>معلومات الزبون</h2>
                <form id="customer-info-form" class="vertical-form">
                    <div class="form-group">
                        <label for="cust-booking-date">تاريخ الحجز:</label>
                        <input type="text" id="cust-booking-date" name="cust-booking-date" readonly>
                    </div>
                    <div class="form-group">
                        <label for="cust-full-name">الاسم الثلاثي واللقب:</label>
                        <input type="text" id="cust-full-name" name="cust-full-name" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-mother-name">اسم الأم الثلاثي:</label>
                        <input type="text" id="cust-mother-name" name="cust-mother-name" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-phone1">رقم الهاتف الأول:</label>
                        <input type="tel" id="cust-phone1" name="cust-phone1" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-phone2">رقم الهاتف الثاني (اختياري):</label>
                        <input type="tel" id="cust-phone2" name="cust-phone2">
                    </div>
                    <div class="form-group">
                        <label for="cust-id">رقم الهوية:</label>
                        <input type="text" id="cust-id" name="cust-id" required>
                    </div>
                     <div class="form-group">
                        <label for="cust-residence-card">رقم بطاقة السكن:</label>
                        <input type="text" id="cust-residence-card" name="cust-residence-card" required>
                    </div>
                     <div class="form-group full-width-group">
                        <label for="cust-address">العنوان الكامل:</label>
                        <textarea id="cust-address" name="cust-address" rows="3" required></textarea>
                    </div>
                     <div class="form-group">
                        <label for="cust-source">مصدر الزبون:</label>
                        <input type="text" id="cust-source" name="cust-source">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الزبون</button>
                </form>
            </section>

            <section class="unit-info-section">
                 <h2>معلومات الوحدة السكنية</h2>
                 <form id="unit-info-form">
                     <div class="form-group">
                        <label for="unit-area-name">اسم المنطقة:</label>
                        <input type="text" id="unit-area-name" name="unit-area-name">
                    </div>
                    <div class="form-group">
                        <label for="unit-number">رقم الوحدة السكنية:</label>
                        <input type="text" id="unit-number" name="unit-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-building-number">رقم المبنى:</label>
                        <input type="text" id="unit-building-number" name="unit-building-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-total-area">المساحة الاجمالية للوحدة:</label>
                        <input type="text" id="unit-total-area" name="unit-total-area">
                    </div>
                     <div class="form-group">
                        <label for="unit-actual-area">المساحة الفعلية للوحدة:</label>
                        <input type="text" id="unit-actual-area" name="unit-actual-area">
                    </div>
                    <div class="form-group">
                        <label for="unit-info-type">نوع الوحدة السكنية:</label>
                         <select id="unit-info-type" name="unit-info-type">
                            <option value="" selected>-- اختر نوع الوحدة --</option>
                            <option value="studio">استوديو</option>
                            <option value="1bedroom">غرفة نوم واحدة</option>
                            <option value="2bedroom">غرفتين نوم</option>
                            <option value="3bedroom">ثلاث غرف نوم</option>
                            <option value="villa">فيلا</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label for="unit-classification">تصنيف الوحدة السكنية:</label>
                        <input type="text" id="unit-classification" name="unit-classification">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الوحدة</button>
                 </form>
            </section>

            <section class="payment-info-section border-box bseondar">
                <h2>معلومات الدفع</h2>
                <form id="payment-info-form">
                    <div class="form-group">
                        <label for="unit-price">سٌعر الوحدة السكنية:</label>
                        <div class="currency-input">
                            <input type="number" id="unit-price" name="unit-price" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="initial-payment">الٌالدفعة الٌأولىلى:</label>
                        <div class="currency-input">
                            <input type="number" id="initial-payment" name="initial-payment" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="payment-method">طريقة التسديد:</label>
                        <select id="payment-method" name="payment-method">
                            <option value="" selected>-- اختر طريقة التسديد --</option>
                            <option value="cash">نقداً</option>
                            <option value="installments">بالتقسيط</option>
                            <option value="bank-transfer">حوالة بنكية</option>
                            <option value="check">صك</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="exception">استثناء:</label>
                        <select id="exception" name="exception">
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الدفع</button>
                </form>
            </section>

            <section class="notes-section b-border-box">
                <h2>ملاحظات</h2>
                <div class="notes-content">
                   <textarea id="custom-notes" name="custom-notes" rows="50" placeholder="اكتب ملاحظاتك هنا..."></textarea>
               </div>
            </section>

            <section class="important-notes-section b-border-box">
                <h3>الملاحظات الهامة</h3>
                <div class="notes-content">
                    <ol>
                        <li>لا تعتبر هذه الاستمارة بمثابة عقد ولا جزء من عقد البيع ولا يترتب أي أي التزامات على الشركه.</li>
                        <li>الحجز ساري فقط لمدة يومان فقط بعد تاريخ الحجز اعلاه و و بعدها سيتم الغاء الحجز تلقائيا في حالة لم لم يتم تسديد الدفعة الاولى لشراء الوحدة الس  السكنية.</li>
                        <li>يحظر على المشتري التنازل عن استمارة حجز الوحدة السكنية للغير.</li>
                        <li>في حالة التعاقد بالنيابة، يؤكد الوكيل على صحة البيانات المقدمة عنه وعن المشتري، و على صحة وكالته، و أن يكون مسؤولا عما وارد من بيانات.</li>
                        <li>يكفل المشتري أنه راجع عقد البيع و فهم شروط العقد و هو مستعد للتوقيع بعد الدفعة الأولى. و يقر بتقديم الضمانات المتفق عليها للقسط المتبقي.</li>
                        <li>يقر المشتري بأن لجنة الحجز تبلغ (1.000.000 دينار عراقي) رسوم إدارية لتنظيم الحجز و تأكيد الحجز، و لا تعود حتى في حالة إلغاء العقد. و هي منفصلة عن سعر الوحدة.</li>
                        <li>في حالة سداد المبلغ في مصرف معتمد من قبل الشركة، يجب إحضار إثبات الدفع خلال فترة صلاحية الحجز. و خلاف ذلك، قد تحجز الشركة الوحدة و تقدم بديلا دون اعتراض، بحد أقصى 15 يوما من التاريخ. و بعد ذلك، يبطل الحق و يُسترد فقط المبلغ المدفوع.</li>
                        <li>في حالة انسحاب المشتري، سيتم استرداد المبلغ خلال شهرين.</li>
                        <li>أنا أقر بأنني قرأت و فهمت هذه الشروط. و أتفق على أن رسوم الحجز غير قابلة للاسترداد. و ألتزم بدفع المبلغ خلال اليوم الأول في خلال ثلاثة أيام أو أفقدي الحق في الوحدة بدون أي انتصاف قانوني.</li>
                    </ol>
                </div>
            </section>
        </main>

        <script src="script.js"></script>
    </div>
</body>
</html>

```style.css
:root {
    --primary-color: #4a6b8a;
    --secondary-color: #8a6b4a;
    --accent-color: #6b4a8a;
    --light-color: #f8f5f2;
    --dark-color: #333333;
    --border-color: #d1c7b7;
    --success-color: #5a8a4a;
    --warning-color: #8a784a;
    --error-color: #8a4a5a;
}

body {
    font-family: 'Segoe UI', Tahoma, 'Arial', sans-serif;
    line-height: 1.8;
    color: var(--dark-color);
    background-color: var(--light-color);
    margin: 0;
    padding: 0;
}

.main-header {
    border-bottom: 5px solid var(--secondary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2em;
    color: white;
    padding: 1.5rem;
    align-items: center;
    justify-content: space-between;
}

.main-header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo svg {
    fill: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.logo-left circle {
    stroke: white;
}

.logo-right rect {
    fill: var(--secondary-color);
}

main {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

section {
    background: white;
    border: 4px solid var(--border-color);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    transition: all 0.25s ease;
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 8px;
}

section::after {
    content: '';
    position: absolute;
    bottom: 0;
    height: 4px;
    width: 100%;
    background: linear-gradient();
}

section :is(h2, ol) {
    padding: 1rem;
    border-radius: 8px;
}

h2 {
    color: var(--primary-color);
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

select#exception {
    width: 8px;
    background: #f8f8f8;
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    width:100%;
    transition: all  0.3s ease;
}

select#exception:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.submit-button {
    display: none;
}

.form-group:last-child {
    margin-bottom: 0rem;
}

.notes-section textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    transition: all 0.3s ease;
    resize: vertical;
    border-color: var(--border-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

section:last-child {
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .main-header {
        flex-direction: column;
        text-align: center;
    }

    .logo-left {
        margin: 1rem;
    }

    section {
        padding: 1rem;
        margin: 1em;
    }
}