<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشهادات - منصة التعلم الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعلم الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">الدورات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="forum.html">المنتدى</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="certificates.html">الشهادات</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">إنشاء حساب</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header bg-primary text-white py-5 mt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold">الشهادات المعتمدة</h1>
                    <p class="lead">احصل على شهادات معتمدة عند إتمام الدورات التعليمية بنجاح</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="certificate-icon">
                        <i class="fas fa-certificate fa-5x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Certificate Verification -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="verification-box text-center">
                        <h5 class="mb-3">التحقق من صحة الشهادة</h5>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="أدخل رقم الشهادة" id="certificate-id">
                            <button class="btn btn-warning" type="button" onclick="verifyCertificate()">
                                <i class="fas fa-search me-2"></i>
                                تحقق
                            </button>
                        </div>
                        <small class="text-muted mt-2 d-block">مثال: CERT-2024-001</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <!-- User Certificates Section -->
            <div class="user-certificates mb-5" id="user-certificates" style="display: none;">
                <h3 class="mb-4">شهاداتي</h3>
                <div class="row" id="user-certificates-container">
                    <!-- User certificates will be loaded here -->
                </div>
            </div>

            <!-- Sample Certificates -->
            <section class="sample-certificates">
                <h3 class="mb-4">نماذج من الشهادات</h3>
                <div class="row">
                    <!-- Certificate 1 -->
                    <div class="col-lg-6 mb-4">
                        <div class="certificate">
                            <div class="certificate-content">
                                <div class="certificate-header text-center mb-4">
                                    <i class="fas fa-graduation-cap fa-3x text-warning mb-3"></i>
                                    <h4 class="text-white">شهادة إتمام الدورة</h4>
                                    <div class="certificate-line"></div>
                                </div>
                                
                                <div class="certificate-body text-center">
                                    <h5 class="text-white mb-3">هذا يشهد أن</h5>
                                    <h3 class="text-warning mb-3">محمد أحمد علي</h3>
                                    <h6 class="text-white mb-3">قد أتم بنجاح دورة</h6>
                                    <h4 class="text-warning mb-4">تطوير المواقع الإلكترونية</h4>
                                    
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="certificate-info">
                                                <small class="text-white">تاريخ الإتمام</small>
                                                <div class="text-warning">15 يناير 2024</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="certificate-info">
                                                <small class="text-white">رقم الشهادة</small>
                                                <div class="text-warning">CERT-2024-001</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="certificate-footer text-center mt-4">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="signature">
                                                <div class="signature-line"></div>
                                                <small class="text-white">أحمد محمد</small>
                                                <div class="text-warning small">المدرب</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="signature">
                                                <div class="signature-line"></div>
                                                <small class="text-white">إدارة المنصة</small>
                                                <div class="text-warning small">المدير العام</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="certificate-actions text-center mt-3">
                                <button class="btn btn-warning me-2" onclick="downloadCertificate('sample1')">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل
                                </button>
                                <button class="btn btn-outline-light" onclick="shareCertificate('sample1')">
                                    <i class="fas fa-share me-2"></i>
                                    مشاركة
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate 2 -->
                    <div class="col-lg-6 mb-4">
                        <div class="certificate" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <div class="certificate-content">
                                <div class="certificate-header text-center mb-4">
                                    <i class="fas fa-award fa-3x text-warning mb-3"></i>
                                    <h4 class="text-white">شهادة التميز</h4>
                                    <div class="certificate-line"></div>
                                </div>
                                
                                <div class="certificate-body text-center">
                                    <h5 class="text-white mb-3">تُمنح هذه الشهادة إلى</h5>
                                    <h3 class="text-warning mb-3">فاطمة خالد</h3>
                                    <h6 class="text-white mb-3">لتفوقها في دورة</h6>
                                    <h4 class="text-warning mb-4">التصميم الجرافيكي</h4>
                                    
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="certificate-info">
                                                <small class="text-white">النتيجة</small>
                                                <div class="text-warning">95%</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="certificate-info">
                                                <small class="text-white">التقدير</small>
                                                <div class="text-warning">ممتاز</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="certificate-footer text-center mt-4">
                                    <div class="signature">
                                        <div class="signature-line mx-auto"></div>
                                        <small class="text-white">منصة التعلم الإلكتروني</small>
                                        <div class="text-warning small">شهادة معتمدة</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="certificate-actions text-center mt-3">
                                <button class="btn btn-warning me-2" onclick="downloadCertificate('sample2')">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل
                                </button>
                                <button class="btn btn-outline-light" onclick="shareCertificate('sample2')">
                                    <i class="fas fa-share me-2"></i>
                                    مشاركة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Certificate Features -->
            <section class="certificate-features py-5">
                <div class="row text-center">
                    <div class="col-12 mb-5">
                        <h3>مميزات شهاداتنا</h3>
                        <p class="lead text-muted">شهادات معتمدة وموثقة رقمياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt fa-3x text-primary"></i>
                        </div>
                        <h5>معتمدة رسمياً</h5>
                        <p class="text-muted">شهادات معتمدة من جهات رسمية</p>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-qrcode fa-3x text-success"></i>
                        </div>
                        <h5>قابلة للتحقق</h5>
                        <p class="text-muted">يمكن التحقق من صحتها عبر رمز QR</p>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-cloud-download-alt fa-3x text-info"></i>
                        </div>
                        <h5>قابلة للتحميل</h5>
                        <p class="text-muted">تحميل بصيغة PDF عالية الجودة</p>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-share-alt fa-3x text-warning"></i>
                        </div>
                        <h5>قابلة للمشاركة</h5>
                        <p class="text-muted">شارك شهادتك على وسائل التواصل</p>
                    </div>
                </div>
            </section>

            <!-- How to Get Certificate -->
            <section class="how-to-get py-5 bg-light rounded">
                <div class="container">
                    <div class="row">
                        <div class="col-12 text-center mb-5">
                            <h3>كيفية الحصول على الشهادة</h3>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 text-center mb-4">
                            <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 60px; height: 60px;">
                                <span class="h4 mb-0">1</span>
                            </div>
                            <h6>التسجيل في الدورة</h6>
                            <p class="text-muted small">سجل في الدورة التي تريد دراستها</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <div class="step-number bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 60px; height: 60px;">
                                <span class="h4 mb-0">2</span>
                            </div>
                            <h6>إتمام المحتوى</h6>
                            <p class="text-muted small">أكمل جميع دروس الدورة والمواد التعليمية</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <div class="step-number bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 60px; height: 60px;">
                                <span class="h4 mb-0">3</span>
                            </div>
                            <h6>اجتياز الاختبار</h6>
                            <p class="text-muted small">احصل على 60% على الأقل في الاختبار النهائي</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <div class="step-number bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 60px; height: 60px;">
                                <span class="h4 mb-0">4</span>
                            </div>
                            <h6>احصل على الشهادة</h6>
                            <p class="text-muted small">ستحصل على شهادة معتمدة فور النجاح</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Certificate Verification Modal -->
    <div class="modal fade" id="verificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">نتيجة التحقق</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="verification-result">
                    <!-- Verification result will be shown here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>منصة التعلم الإلكتروني</h5>
                    <p class="text-muted">منصة تعليمية متكاملة تهدف إلى توفير تعليم عالي الجودة للجميع</p>
                </div>
                <div class="col-md-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="courses.html" class="text-muted">الدورات</a></li>
                        <li><a href="forum.html" class="text-muted">المنتدى</a></li>
                        <li><a href="certificates.html" class="text-muted">الشهادات</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>تواصل معنا</h5>
                    <div class="social-links">
                        <a href="#" class="text-muted me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-instagram fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 منصة التعلم الإلكتروني. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data.js"></script>
    <script>
        // Check if user is logged in and load their certificates
        document.addEventListener('DOMContentLoaded', function() {
            checkUserCertificates();
        });

        function checkUserCertificates() {
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            if (currentUser.id) {
                loadUserCertificates(currentUser);
            }
        }

        function loadUserCertificates(user) {
            const userCertificatesSection = document.getElementById('user-certificates');
            const container = document.getElementById('user-certificates-container');
            
            // Sample user certificates (in real app, this would come from backend)
            const userCertificates = [
                {
                    id: 'CERT-2024-001',
                    courseName: 'تطوير المواقع الإلكترونية',
                    completionDate: '2024-01-15',
                    grade: '85%',
                    instructor: 'أحمد محمد'
                }
            ];

            if (userCertificates.length > 0) {
                userCertificatesSection.style.display = 'block';
                container.innerHTML = userCertificates.map(cert => createUserCertificateHTML(cert)).join('');
            }
        }

        function createUserCertificateHTML(certificate) {
            return `
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-certificate fa-2x text-warning me-3"></i>
                                <div>
                                    <h6 class="mb-1">${certificate.courseName}</h6>
                                    <small class="text-muted">رقم الشهادة: ${certificate.id}</small>
                                </div>
                            </div>
                            <div class="certificate-details">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">تاريخ الإتمام</small>
                                        <div>${formatDate(certificate.completionDate)}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">النتيجة</small>
                                        <div class="text-success fw-bold">${certificate.grade}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="certificate-actions mt-3">
                                <button class="btn btn-primary btn-sm me-2" onclick="viewCertificate('${certificate.id}')">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض
                                </button>
                                <button class="btn btn-success btn-sm me-2" onclick="downloadCertificate('${certificate.id}')">
                                    <i class="fas fa-download me-1"></i>
                                    تحميل
                                </button>
                                <button class="btn btn-info btn-sm" onclick="shareCertificate('${certificate.id}')">
                                    <i class="fas fa-share me-1"></i>
                                    مشاركة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function verifyCertificate() {
            const certificateId = document.getElementById('certificate-id').value.trim();
            
            if (!certificateId) {
                alert('يرجى إدخال رقم الشهادة');
                return;
            }

            // Simulate verification (in real app, this would be an API call)
            const isValid = certificateId === 'CERT-2024-001';
            
            const resultContent = document.getElementById('verification-result');
            
            if (isValid) {
                resultContent.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h5 class="text-success">شهادة صحيحة</h5>
                        <div class="certificate-info mt-3">
                            <p><strong>اسم الحاصل على الشهادة:</strong> محمد أحمد علي</p>
                            <p><strong>اسم الدورة:</strong> تطوير المواقع الإلكترونية</p>
                            <p><strong>تاريخ الإصدار:</strong> 15 يناير 2024</p>
                            <p><strong>المدرب:</strong> أحمد محمد</p>
                        </div>
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
                        <h5 class="text-danger">شهادة غير صحيحة</h5>
                        <p>رقم الشهادة المدخل غير موجود في قاعدة البيانات</p>
                    </div>
                `;
            }

            const modal = new bootstrap.Modal(document.getElementById('verificationModal'));
            modal.show();
        }

        function viewCertificate(certificateId) {
            // This would open the certificate in a new window or modal
            alert(`عرض الشهادة: ${certificateId}`);
        }

        function downloadCertificate(certificateId) {
            // Simulate certificate download
            alert(`جاري تحميل الشهادة: ${certificateId}`);
            
            // In a real application, this would trigger a PDF download
            // window.open(`/api/certificates/${certificateId}/download`, '_blank');
        }

        function shareCertificate(certificateId) {
            // Copy certificate verification URL to clipboard
            const verificationUrl = `${window.location.origin}/certificates.html?verify=${certificateId}`;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(verificationUrl).then(() => {
                    showAlert('تم نسخ رابط التحقق من الشهادة', 'success');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = verificationUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('تم نسخ رابط التحقق من الشهادة', 'success');
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 100px; left: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto dismiss after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // Check if there's a certificate to verify in URL
        const urlParams = new URLSearchParams(window.location.search);
        const verifyId = urlParams.get('verify');
        if (verifyId) {
            document.getElementById('certificate-id').value = verifyId;
            verifyCertificate();
        }
    </script>

    <style>
        .certificate-line {
            width: 100px;
            height: 2px;
            background: #ffc107;
            margin: 0 auto;
        }

        .signature-line {
            width: 80px;
            height: 1px;
            background: #ffc107;
            margin: 10px auto 5px;
        }

        .step-number {
            font-weight: bold;
        }

        .certificate-info {
            margin-bottom: 15px;
        }

        .verification-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</body>
</html>
