document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements - Search
    const clientIdSearch = document.getElementById('clientIdSearch');
    const bookingNumberSearch = document.getElementById('bookingNumberSearch');
    const searchClientBtn = document.getElementById('searchClientBtn');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // DOM Elements - Sections
    const paymentPlanSection = document.getElementById('paymentPlanSection');
    const paymentScheduleSection = document.getElementById('paymentScheduleSection');
    const noClientFoundSection = document.getElementById('noClientFoundSection');
    
    // DOM Elements - Payment Plan Form
    const totalPriceInput = document.getElementById('totalPrice');
    const firstPaymentInput = document.getElementById('firstPayment');
    const monthsCountInput = document.getElementById('monthsCount');
    const monthlyAmountInput = document.getElementById('monthlyAmount');
    const finalPaymentInput = document.getElementById('finalPayment');
    const calculatedRemainingAmount = document.getElementById('calculatedRemainingAmount');
    const calculatePaymentBtn = document.getElementById('calculatePaymentBtn');
    
    // Current client data
    let currentClient = null;
    
    // Event Listeners
    searchClientBtn.addEventListener('click', searchClient);
    tryAgainBtn.addEventListener('click', resetSearch);
    calculatePaymentBtn.addEventListener('click', function() {
        const plan = calculatePaymentPlan();
        savePaymentPlan(plan);
        generatePaymentSchedule(plan);
    });
    
    // Initialize - Check for URL params
    const urlParams = new URLSearchParams(window.location.search);
    const bookingId = urlParams.get('id');
    
    if (bookingId) {
        bookingNumberSearch.value = bookingId;
        searchClient();
    }
    
    // Search client function
    function searchClient() {
        const idNumber = clientIdSearch.value.trim();
        const bookingNumber = bookingNumberSearch.value.trim();
        
        if (!idNumber && !bookingNumber) {
            alert('الرجاء إدخال رقم الهوية أو رقم الحجز للبحث');
            return;
        }
        
        // Get bookings from localStorage
        const bookings = JSON.parse(localStorage.getItem('bookings')) || [];
        
        // Find client by ID or booking number
        const client = bookings.find(booking => 
            (idNumber && booking.idNumber === idNumber) || 
            (bookingNumber && booking.bookingNumber === bookingNumber)
        );
        
        if (client) {
            // Store current client
            currentClient = client;
            
            // Show payment plan section
            showPaymentPlanSection();
            
            // Load existing payment plan if any
            loadPaymentPlan();
        } else {
            // Show not found message
            hideAllSections();
            noClientFoundSection.classList.remove('d-none');
        }
    }
    
    // Calculate payment plan
    function calculatePaymentPlan() {
        const totalPrice = parseFloat(totalPriceInput.value) || 0;
        const firstPayment = parseFloat(firstPaymentInput.value) || 0;
        const monthsCount = parseInt(monthsCountInput.value) || 0;
        const monthlyAmount = parseFloat(monthlyAmountInput.value) || 0;
        const finalPayment = parseFloat(finalPaymentInput.value) || 0;
        
        // Calculate total payments and remaining amount
        const totalMonthlyPayments = monthlyAmount * monthsCount;
        const totalPayments = totalMonthlyPayments + finalPayment; // First payment handled separately
        const remaining = totalPrice - firstPayment - totalPayments;
        
        // Update UI with results
        calculatedRemainingAmount.textContent = formatCurrency(remaining) + ' دينار';
        
        // Set class based on remaining amount
        if (remaining < 0) {
            calculatedRemainingAmount.className = 'text-danger';
        } else if (remaining > 0) {
            calculatedRemainingAmount.className = 'text-warning';
        } else {
            calculatedRemainingAmount.className = 'text-success';
        }
        
        return {
            totalPrice,
            firstPayment,
            monthsCount,
            monthlyAmount,
            finalPayment,
            remaining,
            totalPayments
        };
    }
    
    // Save payment plan
    function savePaymentPlan(plan) {
        if (!currentClient) {
            alert('لم يتم العثور على بيانات الزبون');
            return;
        }
        
        // Get all payment plans
        let paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        
        // Update payment plan for current client
        paymentPlans[currentClient.bookingNumber] = {
            ...plan,
            lastUpdated: new Date().toISOString()
        };
        
        // Save back to localStorage
        localStorage.setItem('paymentPlans', JSON.stringify(paymentPlans));
        
        alert('تم حفظ خطة الدفع بنجاح');
    }
    
    // Load payment plan
    function loadPaymentPlan() {
        if (!currentClient) return;
        
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[currentClient.bookingNumber];
        
        if (plan) {
            totalPriceInput.value = plan.totalPrice;
            firstPaymentInput.value = plan.firstPayment;
            monthsCountInput.value = plan.monthsCount;
            monthlyAmountInput.value = plan.monthlyAmount;
            finalPaymentInput.value = plan.finalPayment;
            calculatedRemainingAmount.textContent = formatCurrency(plan.remaining) + ' دينار';
            
            // Set class based on remaining amount
            if (plan.remaining < 0) {
                calculatedRemainingAmount.className = 'text-danger';
            } else if (plan.remaining > 0) {
                calculatedRemainingAmount.className = 'text-warning';
            } else {
                calculatedRemainingAmount.className = 'text-success';
            }
            
            // Generate payment schedule
            generatePaymentSchedule(plan);
        } else {
            // Initialize with data from booking if available
            if (currentClient.unitPrice && !isNaN(parseFloat(currentClient.unitPrice))) {
                totalPriceInput.value = parseFloat(currentClient.unitPrice);
            }
            if (currentClient.firstPayment && !isNaN(parseFloat(currentClient.firstPayment))) {
                firstPaymentInput.value = parseFloat(currentClient.firstPayment);
            }
        }
    }
    
    // Generate payment schedule
    function generatePaymentSchedule(plan) {
        if (!currentClient) return;
        
        const scheduleBody = document.getElementById('paymentScheduleBody');
        scheduleBody.innerHTML = '';
        
        // Show payment schedule section
        paymentScheduleSection.classList.remove('d-none');
        
        // Get existing payments
        let payments = JSON.parse(localStorage.getItem('payments')) || {};
        let clientPayments = payments[currentClient.bookingNumber] || [];
        
        // Get installment statuses if available
        let installmentStatuses = [];
        if (window.getAllInstallmentStatuses) {
            installmentStatuses = window.getAllInstallmentStatuses(currentClient.bookingNumber);
        }
        
        // Create schedule array
        let schedule = [];
        
        // Add first payment
        const bookingDate = new Date(currentClient.bookingDate);
        schedule.push({
            date: bookingDate,
            amount: plan.firstPayment,
            type: 'الدفعة الأولى',
            status: 'مدفوع',
            isPaid: true
        });
        
        // Add monthly payments
        for (let i = 0; i < plan.monthsCount; i++) {
            const paymentDate = new Date(bookingDate);
            paymentDate.setMonth(paymentDate.getMonth() + i + 1);
            
            // Check if payment is already made
            let isPaid = clientPayments.some(payment => {
                const paymentMonth = new Date(payment.date).getMonth();
                const paymentYear = new Date(payment.date).getFullYear();
                return paymentMonth === paymentDate.getMonth() && 
                       paymentYear === paymentDate.getFullYear();
            });
            
            // Check installment status if available
            if (!isPaid && installmentStatuses.length > i) {
                isPaid = installmentStatuses[i];
            }
            
            schedule.push({
                date: paymentDate,
                amount: plan.monthlyAmount,
                type: 'قسط شهري',
                status: isPaid ? 'مدفوع' : 'غير مدفوع',
                isPaid: isPaid,
                index: i
            });
        }
        
        // Add final payment
        const finalPaymentDate = new Date(bookingDate);
        finalPaymentDate.setMonth(finalPaymentDate.getMonth() + plan.monthsCount + 1);
        schedule.push({
            date: finalPaymentDate,
            amount: plan.finalPayment,
            type: 'دفعة نهائية',
            status: 'غير مدفوع',
            isPaid: false
        });
        
        // Populate schedule table
        schedule.forEach((payment, index) => {
            const row = document.createElement('tr');
            // Format date in DD-MM-YYYY format
            const date = payment.date;
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            const formattedDate = `${day}-${month}-${year}`;
            
            // Add appropriate class based on payment status
            if (payment.isPaid) {
                row.classList.add('table-success');
            } else {
                row.classList.add('table-danger');
            }
            
            let actionButton = '';
            if (payment.type === 'قسط شهري' && !payment.isPaid) {
                actionButton = `
                    <button class="btn btn-sm btn-success mark-as-paid" data-index="${payment.index}">
                        <i class="fas fa-check me-1"></i> تحديد كمدفوع
                    </button>
                `;
            }
            
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${formattedDate}</td>
                <td>${formatCurrency(payment.amount)} دينار</td>
                <td>${payment.type}</td>
                <td>
                    <span class="badge ${payment.isPaid ? 'bg-success' : 'bg-danger'}">
                        ${payment.status}
                    </span>
                </td>
                <td>${actionButton}</td>
            `;
            
            scheduleBody.appendChild(row);
        });
        
        // Add event listeners to mark-as-paid buttons
        const markAsPaidButtons = document.querySelectorAll('.mark-as-paid');
        markAsPaidButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                markInstallmentAsPaid(index);
            });
        });
    }
    
    // Mark installment as paid
    function markInstallmentAsPaid(index) {
        if (!currentClient || !window.updateInstallmentStatus) return;
        
        // Update status
        const success = window.updateInstallmentStatus(currentClient.bookingNumber, index, true);
        
        if (success) {
            // Reload payment plan and regenerate schedule
            const plan = calculatePaymentPlan();
            generatePaymentSchedule(plan);
            
            // Show success message
            alert('تم تحديد القسط كمدفوع بنجاح');
        }
    }
    
    // Show payment plan section
    function showPaymentPlanSection() {
        hideAllSections();
        paymentPlanSection.classList.remove('d-none');
    }
    
    // Hide all sections
    function hideAllSections() {
        paymentPlanSection.classList.add('d-none');
        paymentScheduleSection.classList.add('d-none');
        noClientFoundSection.classList.add('d-none');
    }
    
    // Reset search
    function resetSearch() {
        clientIdSearch.value = '';
        bookingNumberSearch.value = '';
        hideAllSections();
        clientIdSearch.focus();
    }
    
    // Format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ').format(amount);
    }
});