document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements - Search
    const clientIdSearch = document.getElementById('clientIdSearch');
    const bookingNumberSearch = document.getElementById('bookingNumberSearch');
    const searchClientBtn = document.getElementById('searchClientBtn');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // DOM Elements - Sections
    const clientInfoSection = document.getElementById('clientInfoSection');
    const monthlyPaymentStatus = document.getElementById('monthlyPaymentStatus');
    const monthlyInstallmentsSection = document.getElementById('monthlyInstallmentsSection');
    const monthlyPaymentSection = document.getElementById('monthlyPaymentSection');
    const noClientFoundSection = document.getElementById('noClientFoundSection');
    
    // DOM Elements - Client Info
    const clientName = document.getElementById('clientName');
    const clientIdNumber = document.getElementById('clientIdNumber');
    const clientPhone = document.getElementById('clientPhone');
    const clientBookingNumber = document.getElementById('clientBookingNumber');
    const clientBookingDate = document.getElementById('clientBookingDate');
    const clientAddress = document.getElementById('clientAddress');
    
    // DOM Elements - Monthly Payment Status
    const unitTotalPrice = document.getElementById('unitTotalPrice');
    const amountPaid = document.getElementById('amountPaid');
    const remainingAmount = document.getElementById('remainingAmount');
    const totalInstallments = document.getElementById('totalInstallments');
    const paidInstallments = document.getElementById('paidInstallments');
    const remainingInstallments = document.getElementById('remainingInstallments');
    
    // DOM Elements - Monthly Payment Form
    const monthlyPaymentForm = document.getElementById('monthlyPaymentForm');
    const installmentIndex = document.getElementById('installmentIndex');
    const installmentMonth = document.getElementById('installmentMonth');
    const dueDate = document.getElementById('dueDate');
    const installmentAmount = document.getElementById('installmentAmount');
    const paymentDate = document.getElementById('paymentDate');
    const paymentMethodSelect = document.getElementById('paymentMethodSelect');
    const receiptNumber = document.getElementById('receiptNumber');
    const paymentNotes = document.getElementById('paymentNotes');
    const cancelPaymentBtn = document.getElementById('cancelPaymentBtn');
    
    // DOM Elements - Receipt
    const receiptNumberField = document.getElementById('receipt-number');
    const receiptDateField = document.getElementById('receipt-date');
    const receiptNameField = document.getElementById('receipt-name');
    const receiptIdField = document.getElementById('receipt-id');
    const receiptBookingNumberField = document.getElementById('receipt-booking-number');
    const receiptMonthField = document.getElementById('receipt-month');
    const receiptAmountField = document.getElementById('receipt-amount');
    const receiptMethodField = document.getElementById('receipt-method');
    const receiptInstallmentNumberField = document.getElementById('receipt-installment-number');
    const receiptRemainingField = document.getElementById('receipt-remaining');
    const receiptNotesField = document.getElementById('receipt-notes');
    
    // Modals
    const paymentReceiptModal = new bootstrap.Modal(document.getElementById('paymentReceiptModal'));
    const paymentSuccessModal = new bootstrap.Modal(document.getElementById('paymentSuccessModal'));
    
    // Current client data
    let currentClient = null;
    let currentPaymentPlan = null;
    let installmentsSchedule = [];
    
    // Initialize - Check for URL params
    const urlParams = new URLSearchParams(window.location.search);
    const bookingId = urlParams.get('id');
    
    if (bookingId) {
        bookingNumberSearch.value = bookingId;
        searchClient();
    }
    
    // Event Listeners
    searchClientBtn.addEventListener('click', searchClient);
    tryAgainBtn.addEventListener('click', resetSearch);
    monthlyPaymentForm.addEventListener('submit', handleMonthlyPayment);
    cancelPaymentBtn.addEventListener('click', hidePaymentForm);
    
    document.getElementById('printModalReceiptBtn').addEventListener('click', printReceipt);
    document.getElementById('viewReceiptBtn').addEventListener('click', function() {
        paymentSuccessModal.hide();
        setTimeout(() => {
            paymentReceiptModal.show();
        }, 500);
    });
    
    // Search client function
    function searchClient() {
        const idNumber = clientIdSearch.value.trim();
        const bookingNumber = bookingNumberSearch.value.trim();
        
        if (!idNumber && !bookingNumber) {
            alert('الرجاء إدخال رقم الهوية أو رقم الحجز للبحث');
            return;
        }
        
        // Get bookings from localStorage
        const bookings = JSON.parse(localStorage.getItem('bookings')) || [];
        
        // Find client by ID or booking number
        const client = bookings.find(booking => 
            (idNumber && booking.idNumber === idNumber) || 
            (bookingNumber && booking.bookingNumber === bookingNumber)
        );
        
        if (client) {
            // Store current client
            currentClient = client;
            
            // Get payment plan
            loadPaymentPlan();
            
            // Display client information
            displayClientInfo();
            
            // Show client sections
            showClientSections();
            
            // Generate installments schedule
            generateInstallmentsSchedule();
            
            // Display installments table
            updateInstallmentsTable();
        } else {
            // Show not found message
            hideAllSections();
            noClientFoundSection.classList.remove('d-none');
        }
    }
    
    // Format date to DD-MM-YYYY
    function formatDateDMY(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    }
    
    // Format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ').format(amount);
    }
    
    // Get month name in Arabic
    function getArabicMonthName(date) {
        const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return arabicMonths[date.getMonth()];
    }
    
    // Load payment plan
    function loadPaymentPlan() {
        if (!currentClient) return;
        
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        currentPaymentPlan = paymentPlans[currentClient.bookingNumber];
        
        if (!currentPaymentPlan) {
            // Create default payment plan if not exists
            currentPaymentPlan = {
                totalPrice: parseFloat(currentClient.unitPrice) || 0,
                firstPayment: parseFloat(currentClient.firstPayment) || 0,
                monthsCount: 12, // Default to 12 months
                monthlyAmount: parseFloat(currentClient.unitPrice) / 12, // Default monthly amount
                finalPayment: 0,
                remaining: 0
            };
        }
    }
    
    // Display client information
    function displayClientInfo() {
        if (!currentClient || !currentPaymentPlan) return;
        
        // Format date
        const bookingDate = new Date(currentClient.bookingDate);
        const formattedDate = formatDateDMY(bookingDate);
        
        // Client Info
        clientName.textContent = currentClient.fullName + ' ' + (currentClient.lastName || '');
        clientIdNumber.textContent = currentClient.idNumber;
        clientPhone.textContent = currentClient.phone || 'غير متوفر';
        clientBookingNumber.textContent = currentClient.bookingNumber;
        clientBookingDate.textContent = formattedDate;
        clientAddress.textContent = currentClient.fullAddress || 'غير متوفر';
        
        // Get payments data
        const payments = JSON.parse(localStorage.getItem('payments')) || {};
        const clientPayments = payments[currentClient.bookingNumber] || [];
        
        // Calculate total paid
        const totalPaid = clientPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
        
        // Monthly Payment Status
        unitTotalPrice.textContent = formatCurrency(currentPaymentPlan.totalPrice);
        amountPaid.textContent = formatCurrency(totalPaid);
        
        // Calculate remaining amount (subtracting first payment)
        const firstPaymentAmount = currentPaymentPlan.firstPayment || 0;
        const remainingValue = currentPaymentPlan.totalPrice - firstPaymentAmount - totalPaid;
        remainingAmount.textContent = formatCurrency(remainingValue);
        
        // Installments info
        totalInstallments.textContent = currentPaymentPlan.monthsCount;
        paidInstallments.textContent = clientPayments.length;
        remainingInstallments.textContent = Math.max(0, currentPaymentPlan.monthsCount - clientPayments.length);
    }
    
    // Generate installments schedule
    function generateInstallmentsSchedule() {
        if (!currentClient || !currentPaymentPlan) return;
        
        // Get bookingDate as the starting point
        const bookingDate = new Date(currentClient.bookingDate);
        
        // Get payments
        const payments = JSON.parse(localStorage.getItem('payments')) || {};
        const clientPayments = payments[currentClient.bookingNumber] || [];
        
        // Get installment statuses if available
        let installmentStatuses = [];
        if (window.getAllInstallmentStatuses) {
            installmentStatuses = window.getAllInstallmentStatuses(currentClient.bookingNumber);
        }
        
        // Create schedule array
        installmentsSchedule = [];
        
        // Add monthly installments
        for (let i = 0; i < currentPaymentPlan.monthsCount; i++) {
            const installmentDate = new Date(bookingDate);
            installmentDate.setMonth(bookingDate.getMonth() + i + 1);
            
            // Check if payment exists for this month
            let isPaid = clientPayments.some(payment => {
                const paymentDate = new Date(payment.date);
                return paymentDate.getMonth() === installmentDate.getMonth() && 
                       paymentDate.getFullYear() === installmentDate.getFullYear();
            });
            
            // Check installment status if available
            if (!isPaid && installmentStatuses.length > i) {
                isPaid = installmentStatuses[i];
            }
            
            // Add to schedule
            installmentsSchedule.push({
                index: i,
                month: getArabicMonthName(installmentDate) + ' ' + installmentDate.getFullYear(),
                dueDate: formatDateDMY(installmentDate),
                amount: currentPaymentPlan.monthlyAmount,
                isPaid: isPaid,
                installmentDate: installmentDate
            });
        }
    }
    
    // Update installments table
    function updateInstallmentsTable() {
        const tableBody = document.getElementById('installmentsTableBody');
        tableBody.innerHTML = '';
        
        if (installmentsSchedule.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="6" class="text-center">لا توجد أقساط مجدولة</td>`;
            tableBody.appendChild(row);
            return;
        }
        
        // Add rows for each installment
        installmentsSchedule.forEach((installment, index) => {
            const row = document.createElement('tr');
            
            // Add class to highlight due installments
            if (!installment.isPaid && new Date() > installment.installmentDate) {
                row.classList.add('table-danger');
            } else if (installment.isPaid) {
                row.classList.add('table-success');
            }
            
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${installment.month}</td>
                <td>${installment.dueDate}</td>
                <td>${formatCurrency(installment.amount)} دينار</td>
                <td>
                    <span class="badge ${installment.isPaid ? 'bg-success' : 'bg-danger'}">
                        ${installment.isPaid ? 'مدفوع' : 'غير مدفوع'}
                    </span>
                </td>
                <td>
                    ${!installment.isPaid ? `
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-success pay-installment" data-index="${installment.index}">
                                <i class="fas fa-money-bill-wave me-1"></i> دفع
                            </button>
                            <button class="btn btn-sm btn-outline-success mark-as-paid" data-index="${installment.index}">
                                <i class="fas fa-check me-1"></i> تحديد كمدفوع
                            </button>
                        </div>
                    ` : `
                        <button class="btn btn-sm btn-info view-receipt" data-index="${installment.index}">
                            <i class="fas fa-receipt me-1"></i> عرض الإيصال
                        </button>
                    `}
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to pay buttons
        const payButtons = document.querySelectorAll('.pay-installment');
        payButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                showPaymentForm(index);
            });
        });
        
        // Add event listeners to mark-as-paid buttons
        const markAsPaidButtons = document.querySelectorAll('.mark-as-paid');
        markAsPaidButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                markInstallmentAsPaid(index);
            });
        });
        
        // Add event listeners to receipt buttons
        const receiptButtons = document.querySelectorAll('.view-receipt');
        receiptButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                showReceipt(index);
            });
        });
    }
    
    // Show payment form for installment
    function showPaymentForm(index) {
        const installment = installmentsSchedule[index];
        if (!installment) return;
        
        // Set values in form
        installmentIndex.value = index;
        installmentMonth.value = installment.month;
        dueDate.value = installment.dueDate;
        installmentAmount.value = installment.amount;
        
        // Set default payment date to today
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];
        paymentDate.value = formattedToday;
        
        // Show payment form
        monthlyPaymentSection.classList.remove('d-none');
        
        // Scroll to payment form
        monthlyPaymentSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Hide payment form
    function hidePaymentForm() {
        monthlyPaymentSection.classList.add('d-none');
        monthlyPaymentForm.reset();
    }
    
    // Handle monthly payment submission
    function handleMonthlyPayment(e) {
        e.preventDefault();
        
        if (!currentClient) {
            alert('لم يتم العثور على بيانات الزبون');
            return;
        }
        
        // Get form values
        const index = parseInt(installmentIndex.value);
        const installment = installmentsSchedule[index];
        
        if (!installment) {
            alert('حدث خطأ في تحديد القسط');
            return;
        }
        
        // Get payment details
        const paymentDateValue = paymentDate.value;
        const paymentMethodValue = paymentMethodSelect.value;
        const receiptNumberValue = receiptNumber.value;
        const notesValue = paymentNotes.value;
        
        // Validate
        if (!paymentDateValue || !paymentMethodValue || !receiptNumberValue) {
            alert('الرجاء ملء جميع الحقول المطلوبة');
            return;
        }
        
        // Create payment object
        const newPayment = {
            date: paymentDateValue,
            amount: installment.amount,
            method: paymentMethodValue,
            receiptNumber: receiptNumberValue,
            notes: notesValue,
            timestamp: new Date().toISOString(),
            isMonthlyInstallment: true,
            installmentIndex: index,
            installmentMonth: installment.month
        };
        
        // Save payment
        savePayment(newPayment);
        
        // Update UI
        generateInstallmentsSchedule();
        updateInstallmentsTable();
        displayClientInfo();
        
        // Prepare receipt
        prepareReceipt(newPayment, index);
        
        // Show success modal
        document.getElementById('successReceiptNumber').textContent = receiptNumberValue;
        paymentSuccessModal.show();
        
        // Hide payment form
        hidePaymentForm();
    }
    
    // Save payment to localStorage
    function savePayment(payment) {
        // Get all payments
        let allPayments = JSON.parse(localStorage.getItem('payments')) || {};
        
        // Initialize payments for this booking if not exists
        if (!allPayments[currentClient.bookingNumber]) {
            allPayments[currentClient.bookingNumber] = [];
        }
        
        // Add payment
        allPayments[currentClient.bookingNumber].push(payment);
        
        // Save back to localStorage
        localStorage.setItem('payments', JSON.stringify(allPayments));
    }
    
    // Prepare receipt
    function prepareReceipt(payment, index) {
        // Get installment
        const installment = installmentsSchedule[index];
        
        // Calculate remaining amount
        const payments = JSON.parse(localStorage.getItem('payments')) || {};
        const clientPayments = payments[currentClient.bookingNumber] || [];
        const totalPaid = clientPayments.reduce((sum, p) => sum + parseFloat(p.amount), 0);
        const remaining = currentPaymentPlan.totalPrice - totalPaid;
        
        // Format dates
        const paymentDate = new Date(payment.date);
        const formattedPaymentDate = formatDateDMY(paymentDate);
        
        // Set receipt fields
        receiptNumberField.textContent = payment.receiptNumber;
        receiptDateField.textContent = formattedPaymentDate;
        receiptNameField.textContent = currentClient.fullName + ' ' + (currentClient.lastName || '');
        receiptIdField.textContent = currentClient.idNumber;
        receiptBookingNumberField.textContent = currentClient.bookingNumber;
        receiptMonthField.textContent = installment.month;
        receiptAmountField.textContent = formatCurrency(payment.amount) + ' دينار';
        receiptMethodField.textContent = payment.method;
        receiptInstallmentNumberField.textContent = (index + 1);
        receiptRemainingField.textContent = formatCurrency(remaining) + ' دينار';
        receiptNotesField.textContent = payment.notes || 'لا توجد ملاحظات';
    }
    
    // Show receipt for paid installment
    function showReceipt(index) {
        // Get payments
        const payments = JSON.parse(localStorage.getItem('payments')) || {};
        const clientPayments = payments[currentClient.bookingNumber] || [];
        
        // Find payment for this installment
        const payment = clientPayments.find(p => 
            p.isMonthlyInstallment && p.installmentIndex === index
        );
        
        if (!payment) {
            alert('لم يتم العثور على معلومات الدفع لهذا القسط');
            return;
        }
        
        // Prepare receipt
        prepareReceipt(payment, index);
        
        // Show receipt modal
        paymentReceiptModal.show();
    }
    
    // Mark installment as paid without payment
    function markInstallmentAsPaid(index) {
        if (!currentClient || !window.updateInstallmentStatus) return;
        
        // Confirm action
        if (!confirm('هل أنت متأكد من تحديد هذا القسط كمدفوع بدون تسجيل دفعة؟')) {
            return;
        }
        
        // Update installment status
        const success = window.updateInstallmentStatus(currentClient.bookingNumber, index, true);
        
        if (success) {
            // Update UI
            generateInstallmentsSchedule();
            updateInstallmentsTable();
            displayClientInfo();
            
            // Show success notification if available
            if (window.showNotification) {
                window.showNotification('تم تحديد القسط كمدفوع بنجاح', 'success', 3000);
            } else {
                alert('تم تحديد القسط كمدفوع بنجاح');
            }
        }
    }
    
    // Print receipt
    function printReceipt() {
        const receiptContent = document.getElementById('receiptContent');
        const printWindow = window.open('', '_blank');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>إيصال دفع القسط الشهري - مجمع المؤمل السكني</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                    body {
                        font-family: 'Tajawal', sans-serif;
                        padding: 20px;
                    }
                    .receipt-paper {
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                    }
                    .dashed {
                        border-top: 1px dashed #adb5bd;
                        margin: 15px 0;
                    }
                    .receipt-header h3 {
                        color: #1a6e93;
                        font-size: 1.6rem;
                        margin-bottom: 5px;
                    }
                    @media print {
                        body {
                            padding: 0;
                        }
                        .receipt-paper {
                            border: none;
                        }
                    }
                </style>
            </head>
            <body>
                ${receiptContent.outerHTML}
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    }
    
    // Show client sections
    function showClientSections() {
        hideAllSections();
        clientInfoSection.classList.remove('d-none');
        monthlyPaymentStatus.classList.remove('d-none');
        monthlyInstallmentsSection.classList.remove('d-none');
    }
    
    // Hide all sections
    function hideAllSections() {
        clientInfoSection.classList.add('d-none');
        monthlyPaymentStatus.classList.add('d-none');
        monthlyInstallmentsSection.classList.add('d-none');
        monthlyPaymentSection.classList.add('d-none');
        noClientFoundSection.classList.add('d-none');
    }
    
    // Reset search
    function resetSearch() {
        clientIdSearch.value = '';
        bookingNumberSearch.value = '';
        hideAllSections();
        clientIdSearch.focus();
    }
});