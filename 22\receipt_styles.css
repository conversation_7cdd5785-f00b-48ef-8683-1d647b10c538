/* Styling specific to receipts */
.receipt-paper {
    max-width: 800px;
    margin: 0 auto;
    padding: 30px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

.receipt-header {
    text-align: center;
    margin-bottom: 25px;
}

.receipt-header h3 {
    color: #1a6e93;
    font-size: 1.8rem;
    margin-top: 15px;
    margin-bottom: 5px;
}

.receipt-header .subtitle {
    color: #6c757d;
    font-size: 1rem;
}

.receipt-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 2px solid #1a6e93;
    padding: 5px;
    margin: 0 auto;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.receipt-section {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border-left: 4px solid #1a6e93;
}

.receipt-section h5 {
    color: #1a6e93;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.receipt-row .label {
    font-weight: bold;
    color: #495057;
}

.receipt-row .value {
    color: #212529;
}

.payment-summary {
    background-color: #e9f2f7;
    border-left: 4px solid #28a745;
}

.dashed-divider {
    border-top: 1px dashed #adb5bd;
    margin: 15px 0;
}

.signature-section {
    margin-top: 40px;
}

.signature-box {
    text-align: center;
    margin-bottom: 20px;
}

.signature-line {
    width: 90%;
    margin: 10px auto;
    border-top: 1px solid #000;
}

.receipt-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.barcode {
    font-family: 'Libre Barcode 39', cursive;
    font-size: 2.5rem;
    letter-spacing: -1px;
    margin: 15px 0;
}

/* Improved Print-specific styles */
@media print {
    body {
        background-color: #fff;
        margin: 0;
        padding: 0;
    }
    
    .receipt-paper {
        border: none;
        box-shadow: none;
        padding: 0;
        max-width: 100%;
        margin: 0;
    }
    
    .no-print {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .receipt-header {
        text-align: center;
        margin-bottom: 15px;
    }
    
    .receipt-header h3 {
        font-size: 1.5rem;
        margin-top: 10px;
    }
    
    .receipt-logo {
        width: 70px;
        height: 70px;
    }
    
    .receipt-section {
        margin-bottom: 10px;
        padding: 10px;
    }
    
    .receipt-row {
        margin-bottom: 5px;
    }
    
    .signature-section {
        margin-top: 20px;
    }
    
    .signature-box {
        margin-bottom: 10px;
    }
    
    .barcode {
        display: none;
    }
    
    .receipt-footer {
        margin-top: 15px;
        padding-top: 10px;
    }
}