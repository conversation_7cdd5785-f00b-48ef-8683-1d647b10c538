document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements - Search
    const clientIdSearch = document.getElementById('clientIdSearch');
    const bookingNumberSearch = document.getElementById('bookingNumberSearch');
    const searchClientBtn = document.getElementById('searchClientBtn');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // DOM Elements - Sections
    const clientInfoSection = document.getElementById('clientInfoSection');
    const unitInfoSection = document.getElementById('unitInfoSection');
    const paymentInfoSection = document.getElementById('paymentInfoSection');
    const paymentHistorySection = document.getElementById('paymentHistorySection');
    const addPaymentSection = document.getElementById('addPaymentSection');
    const noClientFoundSection = document.getElementById('noClientFoundSection');
    
    // DOM Elements - Client Info
    const clientName = document.getElementById('clientName');
    const clientIdNumber = document.getElementById('clientIdNumber');
    const clientPhone = document.getElementById('clientPhone');
    const clientBookingNumber = document.getElementById('clientBookingNumber');
    const clientBookingDate = document.getElementById('clientBookingDate');
    const clientAddress = document.getElementById('clientAddress');
    
    // DOM Elements - Unit Info
    const unitArea = document.getElementById('unitArea');
    const unitBuilding = document.getElementById('unitBuilding');
    const unitNumber = document.getElementById('unitNumber');
    const unitClass = document.getElementById('unitClass');
    const unitTotalArea = document.getElementById('unitTotalArea');
    const unitActualArea = document.getElementById('unitActualArea');
    
    // DOM Elements - Payment Info
    const unitTotalPrice = document.getElementById('unitTotalPrice');
    const amountPaid = document.getElementById('amountPaid');
    const remainingAmount = document.getElementById('remainingAmount');
    const paymentMethod = document.getElementById('paymentMethod');
    const installmentsCount = document.getElementById('installmentsCount');
    const paymentStatus = document.getElementById('paymentStatus');
    
    // DOM Elements - Forms
    const newPaymentForm = document.getElementById('newPaymentForm');
    const totalPriceInput = document.getElementById('totalPrice');
    const firstPaymentInput = document.getElementById('firstPayment');
    const monthsCountInput = document.getElementById('monthsCount');
    const monthlyAmountInput = document.getElementById('monthlyAmount');
    const finalPaymentInput = document.getElementById('finalPayment');
    const calculatedRemainingAmount = document.getElementById('calculatedRemainingAmount');
    const calculatePaymentBtn = document.getElementById('calculatePaymentBtn');
    
    // DOM Elements - Receipt
    const receiptNumber = document.getElementById('receipt-number');
    const receiptDate = document.getElementById('receipt-date');
    const receiptName = document.getElementById('receipt-name');
    const receiptId = document.getElementById('receipt-id');
    const receiptBookingNumber = document.getElementById('receipt-booking-number');
    const receiptAmount = document.getElementById('receipt-amount');
    const receiptMethod = document.getElementById('receipt-method');
    const receiptInstallmentNumber = document.getElementById('receipt-installment-number');
    const receiptRemaining = document.getElementById('receipt-remaining');
    const receiptNotes = document.getElementById('receipt-notes');
    
    // Modals
    const paymentReceiptModal = new bootstrap.Modal(document.getElementById('paymentReceiptModal'));
    const paymentSuccessModal = new bootstrap.Modal(document.getElementById('paymentSuccessModal'));
    
    // Current client data
    let currentClient = null;
    let currentPayments = [];
    
    // Import functions from payment_functions.js
    const { calculateTotalPaid, updatePaymentStatus, updatePaymentHistory, formatCurrency, formatDateDMY, generatePaymentSchedule, savePlanToStorage, recordPayment } = paymentFunctions;
    
    // Event Listeners
    searchClientBtn.addEventListener('click', searchClient);
    tryAgainBtn.addEventListener('click', resetSearch);
    newPaymentForm.addEventListener('submit', handleNewPayment);
    document.getElementById('printReceiptBtn').addEventListener('click', openReceiptModal);
    document.getElementById('printModalReceiptBtn').addEventListener('click', printReceipt);
    document.getElementById('viewReceiptBtn').addEventListener('click', function() {
        paymentSuccessModal.hide();
        setTimeout(() => {
            paymentReceiptModal.show();
        }, 500);
    });
    
    if (calculatePaymentBtn) {
        calculatePaymentBtn.addEventListener('click', function() {
            generatePaymentSchedule();
            savePlanToStorage();
        });
    }
    
    // Initialize - Check for URL params
    const urlParams = new URLSearchParams(window.location.search);
    const bookingId = urlParams.get('id');
    
    if (bookingId) {
        bookingNumberSearch.value = bookingId;
        searchClient();
    }
    
    // Search client function
    function searchClient() {
        const idNumber = clientIdSearch.value.trim();
        const bookingNumber = bookingNumberSearch.value.trim();
        
        if (!idNumber && !bookingNumber) {
            alert('الرجاء إدخال رقم الهوية أو رقم الحجز للبحث');
            return;
        }
        
        // Get bookings from localStorage
        const bookings = JSON.parse(localStorage.getItem('bookings')) || [];
        
        // Find client by ID or booking number
        const client = bookings.find(booking => 
            (idNumber && booking.idNumber === idNumber) || 
            (bookingNumber && booking.bookingNumber === bookingNumber)
        );
        
        if (client) {
            // Store current client
            currentClient = client;
            
            // Get or initialize payments
            initializePayments(client.bookingNumber);
            
            // Display client information
            displayClientInfo(client);
            
            // Show all sections
            showClientSections();
        } else {
            // Show not found message
            hideAllSections();
            noClientFoundSection.classList.remove('d-none');
        }
    }
    
    // Initialize payments for client
    function initializePayments(bookingNumber) {
        // Get payments from localStorage
        let payments = JSON.parse(localStorage.getItem('payments')) || {};
        
        // Initialize payments for this booking if not exists
        if (!payments[bookingNumber]) {
            payments[bookingNumber] = [];
            localStorage.setItem('payments', JSON.stringify(payments));
        }
        
        // Set current payments
        currentPayments = payments[bookingNumber];
    }
    
    // Display client information
    function displayClientInfo(client) {
        // Format date
        const bookingDate = new Date(client.bookingDate);
        const formattedDate = formatDateDMY(bookingDate);
        
        // Client Info
        clientName.textContent = client.fullName + ' ' + client.lastName;
        clientIdNumber.textContent = client.idNumber;
        clientPhone.textContent = client.phone;
        clientBookingNumber.textContent = client.bookingNumber;
        clientBookingDate.textContent = formattedDate;
        clientAddress.textContent = client.fullAddress;
        
        // Unit Info
        unitArea.textContent = client.areaName;
        unitBuilding.textContent = client.buildingNumber;
        unitNumber.textContent = client.unitNumber;
        unitClass.textContent = client.unitClassification;
        unitTotalArea.textContent = client.totalArea;
        unitActualArea.textContent = client.actualArea;
        
        // Payment Info
        const totalPrice = parseFloat(client.unitPrice.replace(/[^\d.-]/g, '')) || 0;
        unitTotalPrice.textContent = formatCurrency(totalPrice);
        
        // Calculate total paid amount
        const totalPaid = calculateTotalPaid(currentPayments);
        amountPaid.textContent = formatCurrency(totalPaid);
        
        // Calculate remaining amount
        const remaining = totalPrice - totalPaid;
        remainingAmount.textContent = formatCurrency(remaining);
        
        // Payment method and installments
        paymentMethod.textContent = client.paymentMethod;
        installmentsCount.textContent = currentPayments.length;
        
        // Payment status
        updatePaymentStatus(totalPaid, totalPrice, paymentStatus);
        
        // Update payment history
        updatePaymentHistory(currentPayments, document.getElementById('paymentHistoryBody'));
        
        loadPaymentPlan();
    }
    
    // Handle new payment submission
    function handleNewPayment(e) {
        e.preventDefault();
        
        if (!currentClient) {
            alert('لم يتم العثور على بيانات الزبون');
            return;
        }
        
        // Get form values
        const paymentDate = document.getElementById('paymentDate').value;
        const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
        const paymentMethodValue = document.getElementById('paymentMethodSelect').value;
        const receiptNumberValue = document.getElementById('receiptNumber').value;
        const paymentNotes = document.getElementById('paymentNotes').value;
        
        // Validate
        if (!paymentDate || isNaN(paymentAmount) || paymentAmount <= 0 || !paymentMethodValue || !receiptNumberValue) {
            alert('الرجاء ملء جميع الحقول المطلوبة بشكل صحيح');
            return;
        }
        
        // Create new payment object
        const newPayment = {
            date: paymentDate,
            amount: paymentAmount,
            method: paymentMethodValue,
            receiptNumber: receiptNumberValue,
            notes: paymentNotes,
            timestamp: new Date().toISOString()
        };
        
        // Add payment using payment functions
        if (recordPayment) {
            currentPayments = recordPayment(currentClient.bookingNumber, newPayment);
        } else {
            // Fallback to original method if recordPayment is not available
            currentPayments.push(newPayment);
            savePayments();
        }
        
        // Update UI
        displayClientInfo(currentClient);
        
        // Reset form
        newPaymentForm.reset();
        
        // Show success modal
        document.getElementById('successReceiptNumber').textContent = receiptNumberValue;
        
        // Prepare receipt data
        prepareReceiptData(newPayment, currentPayments.length);
        
        // Show success modal
        paymentSuccessModal.show();
    }
    
    // Save payments to localStorage
    function savePayments() {
        // Get all payments
        let allPayments = JSON.parse(localStorage.getItem('payments')) || {};
        
        // Update payments for current client
        allPayments[currentClient.bookingNumber] = currentPayments;
        
        // Save back to localStorage
        localStorage.setItem('payments', JSON.stringify(allPayments));
    }
    
    // Prepare receipt data
    function prepareReceiptData(payment, installmentNumber) {
        // Calculate total and remaining
        const totalPrice = parseFloat(currentClient.unitPrice.replace(/[^\d.-]/g, '')) || 0;
        const totalPaid = calculateTotalPaid(currentPayments);
        const remaining = totalPrice - totalPaid;
        
        // Format date
        const paymentDate = new Date(payment.date);
        const formattedDate = formatDateDMY(paymentDate);
        
        // Set receipt data
        receiptNumber.textContent = payment.receiptNumber;
        receiptDate.textContent = formattedDate;
        receiptName.textContent = currentClient.fullName + ' ' + currentClient.lastName;
        receiptId.textContent = currentClient.idNumber;
        receiptBookingNumber.textContent = currentClient.bookingNumber;
        receiptAmount.textContent = formatCurrency(payment.amount) + ' دينار';
        receiptMethod.textContent = payment.method;
        receiptInstallmentNumber.textContent = installmentNumber;
        receiptRemaining.textContent = formatCurrency(remaining) + ' دينار';
        receiptNotes.textContent = payment.notes || 'لا توجد ملاحظات';
    }
    
    // Open receipt modal
    function openReceiptModal() {
        if (!currentClient || currentPayments.length === 0) {
            alert('لا توجد مدفوعات لطباعة إيصال');
            return;
        }
        
        // Get latest payment
        const latestPayment = [...currentPayments].sort((a, b) => {
            return new Date(b.timestamp) - new Date(a.timestamp);
        })[0];
        
        // Prepare receipt
        prepareReceiptData(latestPayment, currentPayments.length);
        
        // Show modal
        paymentReceiptModal.show();
    }
    
    // Print receipt
    function printReceipt() {
        const receiptContent = document.getElementById('receiptContent');
        const printWindow = window.open('', '_blank');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>إيصال دفع - مجمع المؤمل السكني</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                    body {
                        font-family: 'Tajawal', sans-serif;
                        padding: 20px;
                    }
                    .receipt-paper {
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                    }
                    .dashed {
                        border-top: 1px dashed #adb5bd;
                        margin: 15px 0;
                    }
                    .receipt-header h3 {
                        color: #1a6e93;
                        font-size: 1.6rem;
                        margin-bottom: 5px;
                    }
                    .barcode {
                        font-size: 2rem;
                        letter-spacing: -1px;
                    }
                    @media print {
                        body {
                            padding: 0;
                        }
                        .receipt-paper {
                            border: none;
                        }
                    }
                </style>
            </head>
            <body>
                ${receiptContent.outerHTML}
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    }
    
    // Show client-related sections
    function showClientSections() {
        hideAllSections();
        clientInfoSection.classList.remove('d-none');
        unitInfoSection.classList.remove('d-none');
        paymentInfoSection.classList.remove('d-none');
        paymentHistorySection.classList.remove('d-none');
        addPaymentSection.classList.remove('d-none');
    }
    
    // Hide all sections
    function hideAllSections() {
        clientInfoSection.classList.add('d-none');
        unitInfoSection.classList.add('d-none');
        paymentInfoSection.classList.add('d-none');
        paymentHistorySection.classList.add('d-none');
        addPaymentSection.classList.add('d-none');
        noClientFoundSection.classList.add('d-none');
    }
    
    // Reset search
    function resetSearch() {
        clientIdSearch.value = '';
        bookingNumberSearch.value = '';
        hideAllSections();
        clientIdSearch.focus();
    }
    
    // Load payment plan
    function loadPaymentPlan() {
        if (!currentClient) return;
        
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[currentClient.bookingNumber];
        
        if (plan) {
            totalPriceInput.value = plan.totalPrice;
            firstPaymentInput.value = plan.firstPayment;
            monthsCountInput.value = plan.monthsCount;
            monthlyAmountInput.value = plan.monthlyAmount;
            finalPaymentInput.value = plan.finalPayment;
            calculatedRemainingAmount.textContent = formatCurrency(plan.remaining);
            
            // Set class based on remaining amount
            if (plan.remaining < 0) {
                calculatedRemainingAmount.className = 'text-danger';
            } else if (plan.remaining > 0) {
                calculatedRemainingAmount.className = 'text-warning';
            } else {
                calculatedRemainingAmount.className = 'text-success';
            }
        }
    }
});