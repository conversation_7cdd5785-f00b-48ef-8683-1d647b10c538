<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدورة - منصة التعلم الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعلم الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="courses.html">العودة للدورات</a>
            </div>
        </div>
    </nav>

    <!-- Quiz Header -->
    <section class="quiz-header bg-primary text-white py-5 mt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold">اختبار الدورة</h1>
                    <p class="lead">اختبر معلوماتك واحصل على شهادة إتمام الدورة</p>
                </div>
                <div class="col-md-4">
                    <div class="quiz-timer text-center">
                        <div class="timer-circle bg-white text-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 100px; height: 100px;">
                            <div>
                                <div class="h4 mb-0" id="timer">30:00</div>
                                <small>دقيقة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quiz Progress -->
    <section class="py-3 bg-light">
        <div class="container">
            <div class="quiz-progress">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>التقدم في الاختبار</span>
                    <span id="progress-text">0 من 10 أسئلة</span>
                </div>
                <div class="progress">
                    <div class="progress-bar bg-success" id="quiz-progress-bar" 
                         role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quiz Content -->
    <main class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Quiz Instructions -->
                    <div class="quiz-instructions mb-4" id="quiz-instructions">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار</h5>
                            <ul class="mb-0">
                                <li>يحتوي الاختبار على 10 أسئلة</li>
                                <li>الوقت المحدد للاختبار: 30 دقيقة</li>
                                <li>يجب الحصول على 60% على الأقل للنجاح</li>
                                <li>يمكنك إعادة الاختبار في حالة عدم النجاح</li>
                                <li>اقرأ كل سؤال بعناية قبل الإجابة</li>
                            </ul>
                            <div class="mt-3">
                                <button class="btn btn-primary btn-lg" onclick="startQuiz()">
                                    <i class="fas fa-play me-2"></i>
                                    بدء الاختبار
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Container -->
                    <div id="quiz-container" style="display: none;">
                        <!-- Quiz content will be loaded dynamically -->
                    </div>

                    <!-- Quiz Navigation -->
                    <div class="quiz-navigation mt-4" id="quiz-navigation" style="display: none;">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-secondary" id="prev-btn" onclick="previousQuestion()" disabled>
                                <i class="fas fa-arrow-right me-2"></i>
                                السؤال السابق
                            </button>
                            <div class="question-numbers">
                                <!-- Question numbers will be generated dynamically -->
                            </div>
                            <button class="btn btn-primary" id="next-btn" onclick="nextQuestion()">
                                السؤال التالي
                                <i class="fas fa-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Submit Quiz -->
                    <div class="text-center mt-4" id="submit-section" style="display: none;">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تأكيد إرسال الاختبار</h6>
                            <p class="mb-3">هل أنت متأكد من إرسال إجاباتك؟ لن تتمكن من تعديلها بعد الإرسال.</p>
                            <button class="btn btn-success btn-lg me-2" onclick="submitQuiz()">
                                <i class="fas fa-check me-2"></i>
                                إرسال الإجابات
                            </button>
                            <button class="btn btn-outline-secondary" onclick="reviewAnswers()">
                                <i class="fas fa-eye me-2"></i>
                                مراجعة الإجابات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quiz Sidebar -->
                <div class="col-lg-4">
                    <div class="quiz-sidebar">
                        <!-- Question Overview -->
                        <div class="card mb-4" id="question-overview" style="display: none;">
                            <div class="card-header">
                                <h6 class="mb-0">نظرة عامة على الأسئلة</h6>
                            </div>
                            <div class="card-body">
                                <div class="question-grid" id="question-grid">
                                    <!-- Question grid will be generated dynamically -->
                                </div>
                                <div class="mt-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="question-status answered me-2"></div>
                                        <small>تم الإجابة عليه</small>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="question-status current me-2"></div>
                                        <small>السؤال الحالي</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <div class="question-status unanswered me-2"></div>
                                        <small>لم يتم الإجابة عليه</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quiz Tips -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">نصائح للاختبار</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        اقرأ السؤال بعناية
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-clock text-info me-2"></i>
                                        راقب الوقت المتبقي
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        راجع إجاباتك قبل الإرسال
                                    </li>
                                    <li class="mb-0">
                                        <i class="fas fa-redo text-primary me-2"></i>
                                        يمكنك إعادة الاختبار
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Quiz Results Modal -->
    <div class="modal fade" id="resultsModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">نتائج الاختبار</h5>
                </div>
                <div class="modal-body" id="results-content">
                    <!-- Results will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="retakeQuiz()">
                        إعادة الاختبار
                    </button>
                    <a href="courses.html" class="btn btn-secondary">
                        العودة للدورات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data.js"></script>
    <script>
        // Quiz variables
        let currentQuestionIndex = 0;
        let userAnswers = [];
        let quizTimer;
        let timeRemaining = 30 * 60; // 30 minutes in seconds
        let quizStarted = false;

        // Sample quiz questions
        const quizQuestions = [
            {
                question: "ما هو HTML؟",
                options: [
                    "لغة برمجة للخوادم",
                    "لغة ترميز لإنشاء صفحات الويب",
                    "قاعدة بيانات",
                    "نظام تشغيل"
                ],
                correct: 1
            },
            {
                question: "أي من هذه العناصر يستخدم لإنشاء رابط في HTML؟",
                options: [
                    "<link>",
                    "<a>",
                    "<href>",
                    "<url>"
                ],
                correct: 1
            },
            {
                question: "ما هو CSS؟",
                options: [
                    "لغة برمجة",
                    "لغة تنسيق وتصميم صفحات الويب",
                    "قاعدة بيانات",
                    "خادم ويب"
                ],
                correct: 1
            },
            {
                question: "أي من هذه الخصائص تستخدم لتغيير لون النص في CSS؟",
                options: [
                    "background-color",
                    "color",
                    "font-color",
                    "text-color"
                ],
                correct: 1
            },
            {
                question: "ما هو JavaScript؟",
                options: [
                    "لغة ترميز",
                    "لغة تنسيق",
                    "لغة برمجة للويب",
                    "قاعدة بيانات"
                ],
                correct: 2
            },
            {
                question: "أي من هذه الطرق تستخدم لإضافة JavaScript إلى صفحة HTML؟",
                options: [
                    "<script>",
                    "<js>",
                    "<javascript>",
                    "<code>"
                ],
                correct: 0
            },
            {
                question: "ما هو Bootstrap؟",
                options: [
                    "لغة برمجة",
                    "إطار عمل CSS",
                    "قاعدة بيانات",
                    "خادم ويب"
                ],
                correct: 1
            },
            {
                question: "أي من هذه العناصر يستخدم لإنشاء قائمة مرقمة؟",
                options: [
                    "<ul>",
                    "<ol>",
                    "<list>",
                    "<numbered>"
                ],
                correct: 1
            },
            {
                question: "ما هو الغرض من عنصر <head> في HTML؟",
                options: [
                    "عرض المحتوى الرئيسي",
                    "إضافة الصور",
                    "تحديد معلومات الصفحة",
                    "إنشاء الروابط"
                ],
                correct: 2
            },
            {
                question: "أي من هذه الخصائص تستخدم لجعل النص عريض في CSS؟",
                options: [
                    "font-weight: bold",
                    "text-style: bold",
                    "font-bold: true",
                    "text-weight: bold"
                ],
                correct: 0
            }
        ];

        function startQuiz() {
            quizStarted = true;
            document.getElementById('quiz-instructions').style.display = 'none';
            document.getElementById('quiz-container').style.display = 'block';
            document.getElementById('quiz-navigation').style.display = 'block';
            document.getElementById('question-overview').style.display = 'block';
            
            // Initialize user answers array
            userAnswers = new Array(quizQuestions.length).fill(-1);
            
            // Start timer
            startTimer();
            
            // Generate question grid
            generateQuestionGrid();
            
            // Load first question
            loadQuestion(0);
        }

        function startTimer() {
            quizTimer = setInterval(() => {
                timeRemaining--;
                updateTimerDisplay();
                
                if (timeRemaining <= 0) {
                    clearInterval(quizTimer);
                    autoSubmitQuiz();
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            document.getElementById('timer').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function generateQuestionGrid() {
            const grid = document.getElementById('question-grid');
            grid.innerHTML = '';
            
            for (let i = 0; i < quizQuestions.length; i++) {
                const button = document.createElement('button');
                button.className = 'question-number';
                button.textContent = i + 1;
                button.onclick = () => loadQuestion(i);
                grid.appendChild(button);
            }
        }

        function loadQuestion(index) {
            currentQuestionIndex = index;
            const question = quizQuestions[index];
            
            const container = document.getElementById('quiz-container');
            container.innerHTML = `
                <div class="quiz-container">
                    <div class="question-header mb-4">
                        <h4>السؤال ${index + 1} من ${quizQuestions.length}</h4>
                        <h5>${question.question}</h5>
                    </div>
                    <div class="answers">
                        ${question.options.map((option, optionIndex) => `
                            <div class="answer-option ${userAnswers[index] === optionIndex ? 'selected' : ''}" 
                                 onclick="selectAnswer(${optionIndex})">
                                <input type="radio" name="question_${index}" value="${optionIndex}" 
                                       id="q${index}_${optionIndex}" ${userAnswers[index] === optionIndex ? 'checked' : ''}>
                                <label for="q${index}_${optionIndex}" class="w-100">${option}</label>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            // Update navigation buttons
            updateNavigationButtons();
            
            // Update progress
            updateProgress();
            
            // Update question grid
            updateQuestionGrid();
        }

        function selectAnswer(answerIndex) {
            userAnswers[currentQuestionIndex] = answerIndex;
            
            // Update UI
            const options = document.querySelectorAll('.answer-option');
            options.forEach((option, index) => {
                option.classList.toggle('selected', index === answerIndex);
            });
            
            // Update question grid
            updateQuestionGrid();
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            prevBtn.disabled = currentQuestionIndex === 0;
            
            if (currentQuestionIndex === quizQuestions.length - 1) {
                nextBtn.textContent = 'إنهاء الاختبار';
                nextBtn.onclick = showSubmitSection;
            } else {
                nextBtn.textContent = 'السؤال التالي';
                nextBtn.onclick = nextQuestion;
            }
        }

        function nextQuestion() {
            if (currentQuestionIndex < quizQuestions.length - 1) {
                loadQuestion(currentQuestionIndex + 1);
            }
        }

        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                loadQuestion(currentQuestionIndex - 1);
            }
        }

        function updateProgress() {
            const answeredQuestions = userAnswers.filter(answer => answer !== -1).length;
            const progressPercent = (answeredQuestions / quizQuestions.length) * 100;
            
            document.getElementById('quiz-progress-bar').style.width = progressPercent + '%';
            document.getElementById('progress-text').textContent = 
                `${answeredQuestions} من ${quizQuestions.length} أسئلة`;
        }

        function updateQuestionGrid() {
            const buttons = document.querySelectorAll('.question-number');
            buttons.forEach((button, index) => {
                button.className = 'question-number';
                
                if (index === currentQuestionIndex) {
                    button.classList.add('current');
                } else if (userAnswers[index] !== -1) {
                    button.classList.add('answered');
                } else {
                    button.classList.add('unanswered');
                }
            });
        }

        function showSubmitSection() {
            document.getElementById('submit-section').style.display = 'block';
            document.getElementById('quiz-navigation').style.display = 'none';
        }

        function reviewAnswers() {
            document.getElementById('submit-section').style.display = 'none';
            document.getElementById('quiz-navigation').style.display = 'block';
            loadQuestion(0);
        }

        function submitQuiz() {
            clearInterval(quizTimer);
            
            // Calculate score
            let correctAnswers = 0;
            quizQuestions.forEach((question, index) => {
                if (userAnswers[index] === question.correct) {
                    correctAnswers++;
                }
            });
            
            const score = Math.round((correctAnswers / quizQuestions.length) * 100);
            
            // Show results
            showResults(score, correctAnswers);
        }

        function autoSubmitQuiz() {
            alert('انتهى الوقت المحدد للاختبار. سيتم إرسال إجاباتك تلقائياً.');
            submitQuiz();
        }

        function showResults(score, correctAnswers) {
            const passed = score >= 60;
            const resultClass = passed ? 'success' : 'danger';
            const resultMessage = passed ? 'مبروك! لقد نجحت في الاختبار' : 'للأسف، لم تحقق الدرجة المطلوبة';
            
            const resultsContent = document.getElementById('results-content');
            resultsContent.innerHTML = `
                <div class="text-center">
                    <div class="result-icon mb-3">
                        <i class="fas ${passed ? 'fa-trophy' : 'fa-times-circle'} fa-4x text-${resultClass}"></i>
                    </div>
                    <h3 class="text-${resultClass}">${resultMessage}</h3>
                    <div class="score-display my-4">
                        <div class="score-circle bg-${resultClass} text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 120px; height: 120px;">
                            <div>
                                <div class="h2 mb-0">${score}%</div>
                                <small>النتيجة</small>
                            </div>
                        </div>
                    </div>
                    <div class="result-details">
                        <p class="mb-2">الإجابات الصحيحة: ${correctAnswers} من ${quizQuestions.length}</p>
                        <p class="mb-2">الدرجة المطلوبة للنجاح: 60%</p>
                        ${passed ? 
                            '<p class="text-success"><i class="fas fa-check me-2"></i>يمكنك الآن الحصول على شهادة إتمام الدورة</p>' :
                            '<p class="text-danger"><i class="fas fa-info-circle me-2"></i>يمكنك إعادة الاختبار لتحسين نتيجتك</p>'
                        }
                    </div>
                </div>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('resultsModal'));
            modal.show();
            
            // Save result if user is logged in
            saveQuizResult(score, passed);
        }

        function saveQuizResult(score, passed) {
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            if (currentUser.id) {
                if (!currentUser.quizResults) {
                    currentUser.quizResults = [];
                }
                
                currentUser.quizResults.push({
                    courseId: new URLSearchParams(window.location.search).get('courseId') || 1,
                    score: score,
                    passed: passed,
                    date: new Date().toISOString()
                });
                
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            }
        }

        function retakeQuiz() {
            location.reload();
        }

        // Initialize quiz on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Get course ID from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const courseId = urlParams.get('courseId');
            
            if (!courseId) {
                document.body.innerHTML = `
                    <div class="container mt-5 text-center">
                        <h2>خطأ: لم يتم تحديد الدورة</h2>
                        <a href="courses.html" class="btn btn-primary mt-3">العودة للدورات</a>
                    </div>
                `;
            }
        });
    </script>

    <style>
        .question-number {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 50%;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-number.current {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .question-number.answered {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .question-number.unanswered {
            background: #f8f9fa;
            border-color: #ddd;
        }

        .question-status {
            width: 15px;
            height: 15px;
            border-radius: 50%;
        }

        .question-status.current {
            background: #007bff;
        }

        .question-status.answered {
            background: #28a745;
        }

        .question-status.unanswered {
            background: #f8f9fa;
            border: 2px solid #ddd;
        }

        .timer-circle {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</body>
</html>
