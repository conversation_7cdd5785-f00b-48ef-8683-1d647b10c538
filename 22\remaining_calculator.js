// New utility file for consistent remaining amount calculations
function calculateRemainingBalance(totalPrice, firstPayment, paidInstallments) {
    // Subtract first payment and paid installments from total price
    return totalPrice - firstPayment - paidInstallments;
}

// Format currency with Arabic numerals - convenience function added to this utility
function formatCurrencyValue(amount) {
    return new Intl.NumberFormat('ar-IQ').format(amount);
}

// Export as module
window.remainingCalculator = {
    calculateRemainingBalance,
    formatCurrencyValue
};