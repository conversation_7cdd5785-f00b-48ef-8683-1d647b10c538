@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

:root {
    --primary-color: #1a6e93;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f0f4f8;
    color: var(--dark-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    animation: fadeIn 1s ease-in-out;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.logo svg {
    margin-bottom: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.subtitle {
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 500;
}

/* Navigation Styles */
.main-nav {
    margin-top: -10px;
    margin-bottom: 30px !important;
}

.nav-pills .nav-link {
    border-radius: 30px;
    font-weight: 500;
    padding: 8px 20px;
    transition: all 0.2s;
}

.nav-pills .nav-link.active {
    background-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(26, 110, 147, 0.3);
}

.nav-pills .nav-link:not(.active) {
    color: var(--primary-color);
}

.nav-pills .nav-link:hover:not(.active) {
    background-color: rgba(26, 110, 147, 0.1);
}

/* Form Styles */
.reservation-form {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    animation: slideUp 0.8s ease-out;
}

.card-header {
    background-color: var(--primary-color) !important;
    padding: 15px 20px;
}

.card-header h2 {
    font-size: 1.5rem;
    margin: 0;
}

.card-body {
    padding: 25px;
}

.form-label {
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Database Page Styles */
.database-section {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    animation: slideUp 0.8s ease-out;
}

.table th {
    font-weight: 600;
}

.table-responsive {
    min-height: 400px;
}

/* Receipt Page Styles */
.receipt-section {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    animation: slideUp 0.8s ease-out;
}

.receipt-paper {
    max-width: 700px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    font-size: 0.95rem;
    min-height: 600px;
}

.receipt-header h3 {
    color: var(--primary-color);
    font-size: 1.6rem;
    margin-bottom: 5px;
}

.dashed {
    border-top: 1px dashed #adb5bd;
    margin: 15px 0;
}

.receipt-info {
    line-height: 1.7;
}

.barcode {
    font-family: 'Libre Barcode 39', cursive;
    font-size: 2rem;
    letter-spacing: -1px;
}

/* Receipt Styles */
.logo-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    border: 2px solid #1a6e93;
    padding: 5px;
}

.signature-box {
    margin-top: 30px;
}

.signature-line {
    border-top: 1px solid #000;
    margin-top: 30px;
    width: 90%;
    margin: 0 auto;
}

.payment-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #1a6e93;
}

/* Action Buttons */
.table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.action-btn-group .btn {
    margin-right: 5px;
}

/* Map Section */
.map-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    animation: slideUp 1s ease-out;
}

.map-section h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.map-placeholder {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    overflow: hidden;
    background-color: #f8f9fa;
    height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.map-svg {
    width: 100%;
    height: 250px;
    padding: 10px;
}

/* Customer Info Section */
.customer-info-section {
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.8s ease-in-out;
}

.customer-info-section .card-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)) !important;
    font-weight: 500;
    padding: 12px 20px;
}

.customer-info-section .card-body {
    background-color: #f8f9fa;
}

/* Unit Info Section */
.unit-info-section {
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.8s ease-in-out;
}

.unit-info-section .card-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)) !important;
    font-weight: 500;
    padding: 12px 20px;
}

.unit-info-section .card-body {
    background-color: #f8f9fa;
}

/* Payment Info Section */
.payment-info-section {
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.8s ease-in-out;
}

.payment-info-section .card-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)) !important;
    font-weight: 500;
    padding: 12px 20px;
}

.payment-info-section .card-body {
    background-color: #f8f9fa;
}

/* Payment Portal Styles */
.payment-portal-section {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    animation: slideUp 0.8s ease-out;
}

.client-search-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.client-info-section .card,
.unit-info-section .card,
.payment-info-section .card,
.payment-history-section .card,
.add-payment-section .card {
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.8s ease-in-out;
}

.payment-info-section .card {
    border-color: #28a745;
}

.payment-history-section .card {
    border-color: #17a2b8;
}

.add-payment-section .card {
    border-color: var(--primary-color);
}

.badge {
    font-size: 0.9rem;
    padding: 0.5em 0.8em;
}

/* Important Notes Section */
.important-notes-section {
    border: 2px solid #ffc107;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.8s ease-in-out;
}

.important-notes-section .card-header {
    background: linear-gradient(135deg, #ffc107, #ff9800) !important;
    font-weight: 500;
    padding: 12px 20px;
}

.important-notes-section .card-body {
    background-color: #fff9e6;
    padding: 20px;
}

.important-notes-list {
    padding-right: 20px;
}

.important-notes-list li {
    margin-bottom: 10px;
    line-height: 1.5;
}

/* Footer Styles */
footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    margin-top: 30px;
}

.contact-info {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
}

.copyright {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 5px;
    }
    
    .logo svg {
        width: 80px;
        height: 80px;
    }
}