// ===== MAIN APPLICATION INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initializeAnimations();
    initializeScrollEffects();
    initializeNavbarScroll();
    initializeSliderToggle();
    initializeSearch();
    initializeNewsletter();
    initializeTopBarToggle();
    initializeFooterAnimations();
    initializeNewSections();
    loadCustomizations();
});

// ===== CORE APPLICATION FUNCTIONS =====
function initializeApp() {
    // Initialize AOS (Animate On Scroll)
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }

    // Auto-advance hero slider
    const heroCarousel = document.getElementById('heroCarousel');
    if (heroCarousel) {
        const carousel = new bootstrap.Carousel(heroCarousel, {
            interval: 6000,
            wrap: true,
            pause: 'hover'
        });
    }

    console.log('Educational Platform initialized successfully');
}

function initializeAnimations() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading animation for buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                setTimeout(() => {
                    this.classList.remove('loading');
                }, 2000);
            }
        });
    });
}

function initializeScrollEffects() {
    let lastScrollTop = 0;
    const navbar = document.getElementById('mainNavbar');
    const topBar = document.getElementById('topBar');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Navbar scroll effects
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Auto-hide top bar on scroll down
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            topBar.classList.add('hidden');
        } else if (scrollTop < lastScrollTop) {
            topBar.classList.remove('hidden');
        }
        
        lastScrollTop = scrollTop;
    });
}

function initializeNavbarScroll() {
    // Update active nav link based on scroll position
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    window.addEventListener('scroll', function() {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.pageYOffset >= sectionTop - 200) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
}

function initializeSliderToggle() {
    const sliderToggleBtn = document.getElementById('sliderToggleBtn');
    const heroSlider = document.getElementById('heroSlider');
    let isSliderVisible = false;

    if (sliderToggleBtn && heroSlider) {
        sliderToggleBtn.addEventListener('click', function() {
            isSliderVisible = !isSliderVisible;
            
            if (isSliderVisible) {
                heroSlider.style.display = 'block';
                setTimeout(() => {
                    heroSlider.style.opacity = '1';
                }, 10);
                this.innerHTML = '<i class="fas fa-times"></i><span>إخفاء العروض</span>';
                this.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            } else {
                heroSlider.style.opacity = '0';
                setTimeout(() => {
                    heroSlider.style.display = 'none';
                }, 300);
                this.innerHTML = '<i class="fas fa-play-circle"></i><span>العروض التقديمية</span>';
                this.style.background = 'linear-gradient(135deg, var(--primary-color), var(--secondary-color))';
            }
        });
    }
}

function initializeSearch() {
    const searchInput = document.querySelector('.search-input-premium');
    const searchBtn = document.querySelector('.search-btn-premium');

    if (searchInput && searchBtn) {
        // Search functionality
        function performSearch() {
            const query = searchInput.value.trim();
            if (query) {
                // Simulate search (replace with actual search logic)
                console.log('Searching for:', query);
                
                // Add loading state
                searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                
                setTimeout(() => {
                    searchBtn.innerHTML = '<i class="fas fa-search"></i>';
                    // Redirect to courses page with search query
                    window.location.href = `courses.html?search=${encodeURIComponent(query)}`;
                }, 1000);
            }
        }

        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Search suggestions (placeholder)
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                // Show search suggestions (implement as needed)
                console.log('Search suggestions for:', query);
            }
        });
    }
}

function initializeNewsletter() {
    const newsletterForm = document.getElementById('revolutionaryNewsletter');
    
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const emailInput = this.querySelector('.email-input');
            const submitBtn = this.querySelector('.submit-btn');
            const email = emailInput.value.trim();
            
            if (email && isValidEmail(email)) {
                // Add loading state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التسجيل...';
                submitBtn.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    submitBtn.innerHTML = '<i class="fas fa-check"></i> تم التسجيل!';
                    submitBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                    emailInput.value = '';
                    
                    setTimeout(() => {
                        submitBtn.innerHTML = '<span class="btn-text">ابدأ الآن</span><span class="btn-icon"><i class="fas fa-arrow-left"></i></span>';
                        submitBtn.style.background = 'linear-gradient(135deg, #ffd700, #ffed4e)';
                        submitBtn.disabled = false;
                    }, 3000);
                }, 2000);
            } else {
                alert('يرجى إدخال بريد إلكتروني صحيح');
            }
        });
    }
}

function initializeTopBarToggle() {
    const topBarToggle = document.getElementById('topBarToggle');
    const topBar = document.getElementById('topBar');
    
    if (topBarToggle && topBar) {
        topBarToggle.addEventListener('click', function() {
            topBar.classList.toggle('hidden');
            
            if (topBar.classList.contains('hidden')) {
                this.innerHTML = '<i class="fas fa-chevron-down"></i>';
            } else {
                this.innerHTML = '<i class="fas fa-chevron-up"></i>';
            }
        });
    }
}

function initializeFooterAnimations() {
    // Animate footer counters when they come into view
    const counters = document.querySelectorAll('.counter-number');
    
    const animateCounter = (element) => {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = 2500;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number with commas for thousands
            const formattedNumber = Math.floor(current).toLocaleString();
            element.textContent = formattedNumber;
        }, 16);
    };

    // Intersection Observer for counter animation
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

function initializeNewSections() {
    initializeStoriesStats();
    initializeCTAAnimations();
    initializeInstructorCards();
}

// Initialize stories statistics counters
function initializeStoriesStats() {
    const statNumbers = document.querySelectorAll('.stories-stats .stat-number');
    
    const animateCounter = (element) => {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = 2500;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number with commas for thousands
            const formattedNumber = Math.floor(current).toLocaleString();
            element.textContent = formattedNumber;
        }, 16);
    };

    // Intersection Observer for counter animation
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });
}

// Initialize CTA animations
function initializeCTAAnimations() {
    const ctaSection = document.querySelector('.cta-section');
    if (!ctaSection) return;

    // Parallax effect for CTA shapes
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const shapes = ctaSection.querySelectorAll('.shape');
        
        shapes.forEach((shape, index) => {
            const speed = 0.5 + (index * 0.2);
            const yPos = -(scrolled * speed);
            shape.style.transform = `translateY(${yPos}px)`;
        });
    });

    // CTA buttons hover effects
    const ctaButtons = document.querySelectorAll('.btn-primary-cta, .btn-secondary-cta');
    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Initialize instructor cards interactions
function initializeInstructorCards() {
    const instructorCards = document.querySelectorAll('.instructor-card');
    
    instructorCards.forEach(card => {
        // Add tilt effect on mouse move
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px)`;
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
        });
    });

    // Social links click tracking
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // You can add actual social media links here
            console.log('Social link clicked:', this.className);
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function loadCustomizations() {
    // Load any saved customizations from localStorage
    const savedCustomizations = localStorage.getItem('platformCustomizations');
    if (savedCustomizations) {
        try {
            const customizations = JSON.parse(savedCustomizations);
            applyCustomizations(customizations);
        } catch (e) {
            console.error('Error loading customizations:', e);
        }
    }
}

function applyCustomizations(customizations) {
    // Apply saved customizations (colors, images, etc.)
    if (customizations.primaryColor) {
        document.documentElement.style.setProperty('--primary-color', customizations.primaryColor);
    }
    if (customizations.secondaryColor) {
        document.documentElement.style.setProperty('--secondary-color', customizations.secondaryColor);
    }
    // Add more customization applications as needed
}

// ===== GLOBAL ERROR HANDLING =====
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
});

// ===== PERFORMANCE MONITORING =====
window.addEventListener('load', function() {
    // Log page load time
    const loadTime = performance.now();
    console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
});
