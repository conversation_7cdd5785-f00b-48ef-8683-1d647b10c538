// Main JavaScript file for the educational platform

// Global variables
let currentUser = null;
let currentCourse = null;
let currentQuiz = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initializeAnimations();
    initializeScrollEffects();
    loadCustomizations();
});

function initializeApp() {
    // Check if user is logged in
    checkUserLogin();

    // Load page-specific content
    const currentPage = getCurrentPage();

    switch(currentPage) {
        case 'index':
            loadPopularCourses();
            initializeHeroSlider();
            break;
        case 'courses':
            loadAllCourses();
            initializeFilters();
            break;
        case 'course-details':
            loadCourseDetails();
            break;
        case 'quiz':
            loadQuiz();
            break;
        case 'forum':
            loadForumPosts();
            break;
        case 'profile':
            loadUserProfile();
            break;
        case 'dashboard':
            loadDashboard();
            break;
        case 'certificates':
            loadCertificates();
            break;
    }

    // Initialize common features
    initializeSearch();
    initializeNavigation();
    initializeTooltips();
}

function initializeAnimations() {
    // Add entrance animations to elements
    const animatedElements = document.querySelectorAll('.animate-on-scroll');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(el => observer.observe(el));
}

function initializeScrollEffects() {
    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    const scrollIndicator = document.getElementById('scrollIndicator');

    window.addEventListener('scroll', () => {
        // Navbar effect
        if (window.scrollY > 100) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }

        // Scroll progress indicator
        if (scrollIndicator) {
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrollProgress = (scrollTop / scrollHeight) * 100;
            scrollIndicator.style.transform = `scaleX(${scrollProgress / 100})`;
        }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Back to top button
    const backToTopBtn = createBackToTopButton();
    document.body.appendChild(backToTopBtn);

    window.addEventListener('scroll', () => {
        if (window.scrollY > 500) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });
}

function createBackToTopButton() {
    const button = document.createElement('button');
    button.innerHTML = '<i class="fas fa-arrow-up"></i>';
    button.className = 'back-to-top';
    button.setAttribute('aria-label', 'العودة للأعلى');

    button.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    return button;
}

function initializeHeroSlider() {
    // Auto-advance hero slider
    const heroCarousel = document.getElementById('heroCarousel');
    if (heroCarousel) {
        const carousel = new bootstrap.Carousel(heroCarousel, {
            interval: 6000,
            wrap: true,
            pause: 'hover'
        });
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').pop().split('.')[0];
    return page || 'index';
}

function checkUserLogin() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateNavigationForLoggedInUser();
    }
}

function updateNavigationForLoggedInUser() {
    const loginLink = document.querySelector('a[href="login.html"]');
    const registerLink = document.querySelector('a[href="register.html"]');
    
    if (loginLink && registerLink && currentUser) {
        loginLink.innerHTML = `<i class="fas fa-user me-2"></i>${currentUser.name}`;
        loginLink.href = 'profile.html';
        registerLink.innerHTML = '<i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج';
        registerLink.href = '#';
        registerLink.onclick = logout;
    }
}

function logout() {
    localStorage.removeItem('currentUser');
    currentUser = null;
    window.location.href = 'index.html';
}

// Course loading functions
function loadPopularCourses() {
    const container = document.getElementById('popular-courses-slider');
    if (!container) return;

    const popularCourses = coursesData.slice(0, 6);

    // Create carousel slides (3 courses per slide)
    const slides = [];
    for (let i = 0; i < popularCourses.length; i += 3) {
        const coursesInSlide = popularCourses.slice(i, i + 3);
        const slideHTML = `
            <div class="carousel-item ${i === 0 ? 'active' : ''}">
                <div class="row g-4">
                    ${coursesInSlide.map(course => `
                        <div class="col-md-4">
                            ${createCourseCard(course)}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        slides.push(slideHTML);
    }

    container.innerHTML = slides.join('');
}

function loadAllCourses() {
    const container = document.getElementById('courses-container');
    if (!container) return;
    
    container.innerHTML = coursesData.map(course => createCourseCard(course)).join('');
}

function createCourseCard(course) {
    return `
        <div class="course-card hover-lift">
            <div class="course-image" style="background-image: url('${course.image}')">
                <div class="course-price">${course.price}</div>
                <div class="course-overlay">
                    <a href="course-details.html?id=${course.id}" class="btn btn-light btn-sm">
                        <i class="fas fa-eye me-2"></i>
                        معاينة
                    </a>
                </div>
            </div>
            <div class="course-content">
                <div class="course-category">
                    <span class="badge bg-primary">${course.category}</span>
                    <span class="badge bg-secondary">${course.level}</span>
                </div>
                <h5 class="course-title mb-2">${course.title}</h5>
                <p class="course-description text-muted">${course.description}</p>

                <div class="course-instructor d-flex align-items-center mb-3">
                    <img src="https://via.placeholder.com/40x40/4285f4/ffffff?text=${course.instructor.charAt(0)}"
                         alt="${course.instructor}" class="rounded-circle me-2">
                    <div>
                        <small class="text-muted">المدرب</small>
                        <div class="fw-semibold">${course.instructor}</div>
                    </div>
                </div>

                <div class="course-meta d-flex justify-content-between align-items-center mb-3">
                    <div class="rating">
                        <i class="fas fa-star"></i>
                        <span>${course.rating}</span>
                        <small class="text-muted">(${course.students})</small>
                    </div>
                    <div class="course-duration">
                        <i class="fas fa-clock text-muted"></i>
                        <span>${course.duration}</span>
                    </div>
                </div>

                <div class="course-stats row text-center">
                    <div class="col-4">
                        <small class="text-muted">الطلاب</small>
                        <div class="fw-bold">${course.students}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الدروس</small>
                        <div class="fw-bold">${course.lessons.length}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">المدة</small>
                        <div class="fw-bold">${course.duration}</div>
                    </div>
                </div>

                <div class="course-actions mt-3">
                    <a href="course-details.html?id=${course.id}" class="btn btn-primary w-100 modern-btn">
                        <i class="fas fa-play me-2"></i>
                        ابدأ التعلم
                    </a>
                </div>
            </div>
        </div>
    `;
}

function loadCourseDetails() {
    const urlParams = new URLSearchParams(window.location.search);
    const courseId = parseInt(urlParams.get('id'));
    const course = coursesData.find(c => c.id === courseId);
    
    if (!course) {
        document.body.innerHTML = '<div class="container mt-5"><h2>الدورة غير موجودة</h2></div>';
        return;
    }
    
    currentCourse = course;
    
    // Update page title
    document.title = `${course.title} - منصة التعلم الإلكتروني`;
    
    // Load course content
    const container = document.getElementById('course-content');
    if (container) {
        container.innerHTML = createCourseDetailsHTML(course);
    }
    
    // Initialize course features
    initializeCourseEnrollment();
    initializeLessonNavigation();
}

function createCourseDetailsHTML(course) {
    return `
        <div class="row">
            <div class="col-lg-8">
                <div class="course-header mb-4">
                    <h1>${course.title}</h1>
                    <p class="lead">${course.description}</p>
                    <div class="course-meta d-flex gap-4 mb-3">
                        <span><i class="fas fa-user"></i> ${course.instructor}</span>
                        <span><i class="fas fa-star text-warning"></i> ${course.rating}</span>
                        <span><i class="fas fa-users"></i> ${course.students} طالب</span>
                        <span><i class="fas fa-clock"></i> ${course.duration}</span>
                    </div>
                </div>
                
                <div class="course-lessons">
                    <h3>محتوى الدورة</h3>
                    <div class="lessons-list">
                        ${course.lessons.map((lesson, index) => `
                            <div class="lesson-item d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                                <div>
                                    <h6>${lesson.title}</h6>
                                    <small class="text-muted">${lesson.duration}</small>
                                </div>
                                <div>
                                    ${lesson.completed ? 
                                        '<i class="fas fa-check-circle text-success"></i>' : 
                                        '<i class="fas fa-play-circle text-primary"></i>'
                                    }
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="course-sidebar bg-light p-4 rounded">
                    <div class="course-price mb-3">
                        <h3 class="text-primary">${course.price}</h3>
                    </div>
                    <button class="btn btn-primary btn-lg w-100 mb-3" onclick="enrollInCourse(${course.id})">
                        التسجيل في الدورة
                    </button>
                    <div class="course-includes">
                        <h6>تشمل الدورة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-video text-primary me-2"></i> ${course.lessons.length} درس فيديو</li>
                            <li><i class="fas fa-file-alt text-primary me-2"></i> مواد تعليمية</li>
                            <li><i class="fas fa-certificate text-primary me-2"></i> شهادة إتمام</li>
                            <li><i class="fas fa-infinity text-primary me-2"></i> وصول مدى الحياة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function enrollInCourse(courseId) {
    if (!currentUser) {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'login.html';
        return;
    }
    
    // Add course to user's enrolled courses
    if (!currentUser.enrolledCourses.includes(courseId)) {
        currentUser.enrolledCourses.push(courseId);
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        alert('تم التسجيل في الدورة بنجاح!');
        
        // Update button
        const enrollBtn = event.target;
        enrollBtn.innerHTML = 'مسجل في الدورة';
        enrollBtn.disabled = true;
        enrollBtn.classList.remove('btn-primary');
        enrollBtn.classList.add('btn-success');
    } else {
        alert('أنت مسجل في هذه الدورة بالفعل');
    }
}

// Quiz functions
function loadQuiz() {
    const urlParams = new URLSearchParams(window.location.search);
    const courseId = parseInt(urlParams.get('courseId'));
    const quiz = quizData.find(q => q.courseId === courseId);
    
    if (!quiz) {
        document.body.innerHTML = '<div class="container mt-5"><h2>الاختبار غير موجود</h2></div>';
        return;
    }
    
    currentQuiz = quiz;
    displayQuiz(quiz);
}

function displayQuiz(quiz) {
    const container = document.getElementById('quiz-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="quiz-header text-center mb-4">
            <h2>اختبار الدورة</h2>
            <p class="lead">أجب على جميع الأسئلة واضغط على "إرسال الإجابات"</p>
        </div>
        <form id="quiz-form">
            ${quiz.questions.map((question, index) => `
                <div class="question mb-4">
                    <h4>السؤال ${index + 1}: ${question.question}</h4>
                    <div class="answers">
                        ${question.options.map((option, optionIndex) => `
                            <div class="answer-option" onclick="selectAnswer(${index}, ${optionIndex})">
                                <input type="radio" name="question_${index}" value="${optionIndex}" id="q${index}_${optionIndex}" style="display: none;">
                                <label for="q${index}_${optionIndex}" class="w-100">${option}</label>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
            <div class="text-center">
                <button type="button" class="btn btn-primary btn-lg" onclick="submitQuiz()">
                    إرسال الإجابات
                </button>
            </div>
        </form>
        <div id="quiz-results" style="display: none;"></div>
    `;
}

function selectAnswer(questionIndex, answerIndex) {
    // Remove previous selection
    const questionDiv = document.querySelectorAll('.question')[questionIndex];
    questionDiv.querySelectorAll('.answer-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Add selection to clicked option
    const selectedOption = questionDiv.querySelectorAll('.answer-option')[answerIndex];
    selectedOption.classList.add('selected');
    
    // Check the radio button
    const radioButton = selectedOption.querySelector('input[type="radio"]');
    radioButton.checked = true;
}

function submitQuiz() {
    const form = document.getElementById('quiz-form');
    const formData = new FormData(form);
    const answers = [];
    
    // Collect answers
    for (let i = 0; i < currentQuiz.questions.length; i++) {
        const answer = formData.get(`question_${i}`);
        answers.push(answer ? parseInt(answer) : -1);
    }
    
    // Calculate score
    let correctAnswers = 0;
    currentQuiz.questions.forEach((question, index) => {
        if (answers[index] === question.correct) {
            correctAnswers++;
        }
    });
    
    const score = Math.round((correctAnswers / currentQuiz.questions.length) * 100);
    
    // Display results
    displayQuizResults(score, correctAnswers, currentQuiz.questions.length, answers);
}

function displayQuizResults(score, correct, total, userAnswers) {
    const resultsDiv = document.getElementById('quiz-results');
    const quizForm = document.getElementById('quiz-form');
    
    quizForm.style.display = 'none';
    resultsDiv.style.display = 'block';
    
    let resultClass = 'success';
    let resultMessage = 'ممتاز!';
    
    if (score < 60) {
        resultClass = 'danger';
        resultMessage = 'يحتاج إلى تحسين';
    } else if (score < 80) {
        resultClass = 'warning';
        resultMessage = 'جيد';
    }
    
    resultsDiv.innerHTML = `
        <div class="quiz-results text-center">
            <div class="alert alert-${resultClass}">
                <h3>${resultMessage}</h3>
                <p>نتيجتك: ${score}%</p>
                <p>الإجابات الصحيحة: ${correct} من ${total}</p>
            </div>
            <div class="mt-4">
                <button class="btn btn-primary me-2" onclick="retakeQuiz()">إعادة الاختبار</button>
                <a href="courses.html" class="btn btn-secondary">العودة للدورات</a>
            </div>
        </div>
    `;
    
    // Save quiz result
    if (currentUser && score >= 60) {
        saveQuizResult(score);
    }
}

function retakeQuiz() {
    location.reload();
}

function saveQuizResult(score) {
    // Save quiz result to user data
    if (!currentUser.quizResults) {
        currentUser.quizResults = [];
    }
    
    currentUser.quizResults.push({
        courseId: currentQuiz.courseId,
        score: score,
        date: new Date().toISOString()
    });
    
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', performSearch);
    }
}

function performSearch() {
    const query = document.getElementById('search-input').value.toLowerCase();
    const filteredCourses = coursesData.filter(course => 
        course.title.toLowerCase().includes(query) ||
        course.description.toLowerCase().includes(query) ||
        course.instructor.toLowerCase().includes(query)
    );
    
    const container = document.getElementById('courses-container');
    if (container) {
        container.innerHTML = filteredCourses.map(course => createCourseCard(course)).join('');
    }
}

// Filter functionality
function initializeFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const levelFilter = document.getElementById('level-filter');
    const priceFilter = document.getElementById('price-filter');
    
    if (categoryFilter) categoryFilter.addEventListener('change', applyFilters);
    if (levelFilter) levelFilter.addEventListener('change', applyFilters);
    if (priceFilter) priceFilter.addEventListener('change', applyFilters);
}

function applyFilters() {
    const category = document.getElementById('category-filter')?.value;
    const level = document.getElementById('level-filter')?.value;
    const price = document.getElementById('price-filter')?.value;
    
    let filteredCourses = coursesData;
    
    if (category && category !== 'all') {
        filteredCourses = filteredCourses.filter(course => course.category === category);
    }
    
    if (level && level !== 'all') {
        filteredCourses = filteredCourses.filter(course => course.level === level);
    }
    
    if (price && price !== 'all') {
        if (price === 'free') {
            filteredCourses = filteredCourses.filter(course => course.price === 'مجاني');
        } else if (price === 'paid') {
            filteredCourses = filteredCourses.filter(course => course.price !== 'مجاني');
        }
    }
    
    const container = document.getElementById('courses-container');
    if (container) {
        container.innerHTML = filteredCourses.map(course => createCourseCard(course)).join('');
    }
}

// Navigation functions
function initializeNavigation() {
    // Add active class to current page
    const currentPage = getCurrentPage();
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.includes(currentPage)) {
            link.classList.add('active');
        }
    });
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function showLoading(element) {
    element.innerHTML = '<div class="text-center"><div class="loading"></div></div>';
}

function hideLoading() {
    // Remove loading indicators
    document.querySelectorAll('.loading').forEach(loader => {
        loader.parentElement.parentElement.style.display = 'none';
    });
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('خطأ في التطبيق:', e.error);
});

// Enhanced interactions and effects
function initializeEnhancedEffects() {
    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax-element');

        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Add ripple effect to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Initialize enhanced effects
document.addEventListener('DOMContentLoaded', initializeEnhancedEffects);

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Performance optimization
function optimizePerformance() {
    // Debounce scroll events
    let scrollTimeout;
    const originalScrollHandler = window.onscroll;

    window.onscroll = function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(() => {
            if (originalScrollHandler) {
                originalScrollHandler();
            }
        }, 16); // ~60fps
    };

    // Preload critical resources
    const criticalImages = [
        'https://via.placeholder.com/600x400/4285f4/ffffff?text=منصة+التعلم',
        'https://via.placeholder.com/600x400/f5576c/ffffff?text=شهادات+معتمدة',
        'https://via.placeholder.com/600x400/00f2fe/ffffff?text=مجتمع+تفاعلي'
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', optimizePerformance);

// Load customizations from admin panel
function loadCustomizations() {
    // Load custom theme
    const customTheme = JSON.parse(localStorage.getItem('customTheme'));
    if (customTheme) {
        applyCustomTheme(customTheme);
    }

    // Load site settings
    const siteSettings = JSON.parse(localStorage.getItem('currentSiteSettings'));
    if (siteSettings) {
        applySiteSettings(siteSettings);
    }

    // Load custom content
    loadCustomContent();
}

function applyCustomTheme(theme) {
    const root = document.documentElement;
    root.style.setProperty('--primary-color', theme.primaryColor);
    root.style.setProperty('--secondary-color', theme.secondaryColor);
    root.style.setProperty('--success-color', theme.successColor);
    root.style.setProperty('--warning-color', theme.warningColor);

    if (theme.siteFont) {
        document.body.style.fontFamily = theme.siteFont;
    }

    // Update gradient variables
    root.style.setProperty('--gradient-primary', `linear-gradient(135deg, ${theme.primaryColor} 0%, ${theme.secondaryColor} 100%)`);
}

function applySiteSettings(settings) {
    // Update site name in navbar
    const navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand && settings.siteName) {
        navbarBrand.innerHTML = `<i class="fas fa-graduation-cap me-2"></i>${settings.siteName}`;
    }

    // Update footer contact info
    const footerEmail = document.querySelector('.footer-email');
    if (footerEmail && settings.siteEmail) {
        footerEmail.textContent = settings.siteEmail;
    }

    const footerPhone = document.querySelector('.footer-phone');
    if (footerPhone && settings.sitePhone) {
        footerPhone.textContent = settings.sitePhone;
    }

    // Update page title
    if (settings.siteName) {
        document.title = `${settings.siteName} - ${document.title.split(' - ')[1] || 'منصة التعلم الإلكتروني'}`;
    }
}

function loadCustomContent() {
    // Load hero content
    const heroContent = JSON.parse(localStorage.getItem('content_hero'));
    if (heroContent) {
        const heroTitle = document.querySelector('.hero-slide h1');
        if (heroTitle && heroContent.title) {
            heroTitle.innerHTML = heroContent.title + '<span class="gradient-text d-block">مع أفضل الخبراء</span>';
        }

        const heroSubtitle = document.querySelector('.hero-slide .lead');
        if (heroSubtitle && heroContent.subtitle) {
            heroSubtitle.textContent = heroContent.subtitle;
        }

        const heroButton1 = document.querySelector('.hero-slide .btn-warning');
        if (heroButton1 && heroContent.button1) {
            heroButton1.innerHTML = `<i class="fas fa-play me-2"></i>${heroContent.button1}`;
        }

        const heroButton2 = document.querySelector('.hero-slide .btn-outline-light');
        if (heroButton2 && heroContent.button2) {
            heroButton2.innerHTML = `<i class="fas fa-user-plus me-2"></i>${heroContent.button2}`;
        }
    }

    // Load courses section content
    const coursesContent = JSON.parse(localStorage.getItem('content_courses'));
    if (coursesContent) {
        const coursesTitle = document.querySelector('.courses-section .section-title');
        if (coursesTitle && coursesContent.title) {
            coursesTitle.textContent = coursesContent.title;
        }

        const coursesDescription = document.querySelector('.courses-section .lead');
        if (coursesDescription && coursesContent.description) {
            coursesDescription.textContent = coursesContent.description;
        }
    }
}
