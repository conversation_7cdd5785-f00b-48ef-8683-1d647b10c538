document.addEventListener('DOMContentLoaded', function() {
    // Create notification element if it doesn't exist
    if (!document.getElementById('notification-system')) {
        const notificationElement = document.createElement('div');
        notificationElement.id = 'notification-system';
        notificationElement.className = 'notification-container';
        document.body.appendChild(notificationElement);
        
        // Add styles for notifications
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                left: 20px;
                right: 20px;
                z-index: 9999;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            
            .notification {
                padding: 15px 25px;
                margin-bottom: 10px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                color: white;
                font-weight: 500;
                opacity: 0;
                transform: translateY(-20px);
                transition: all 0.4s ease;
                max-width: 400px;
                text-align: center;
            }
            
            .notification.success {
                background-color: #28a745;
            }
            
            .notification.error {
                background-color: #dc3545;
            }
            
            .notification.warning {
                background-color: #ffc107;
                color: #212529;
            }
            
            .notification.info {
                background-color: #17a2b8;
            }
            
            .notification.show {
                opacity: 1;
                transform: translateY(0);
            }
        `;
        document.head.appendChild(styleElement);
    }
    
    // Global notification function
    window.showNotification = function(message, type = 'success', duration = 3000) {
        const container = document.getElementById('notification-system');
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        container.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Hide and remove notification after duration
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                container.removeChild(notification);
            }, 400);
        }, duration);
    };
});