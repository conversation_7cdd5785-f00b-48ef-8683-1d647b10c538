<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول المدير - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .admin-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .admin-logo i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-admin {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
        }
        
        .back-link {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: #ffc107;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للموقع
    </a>
    
    <div class="admin-login-container">
        <div class="admin-logo">
            <i class="fas fa-shield-alt"></i>
            <h3 class="fw-bold">لوحة التحكم</h3>
            <p class="text-muted">تسجيل دخول المدير</p>
        </div>
        
        <form id="adminLoginForm">
            <div class="mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" class="form-control" id="username" required placeholder="أدخل اسم المستخدم">
                </div>
            </div>
            
            <div class="mb-4">
                <label for="password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" class="form-control" id="password" required placeholder="أدخل كلمة المرور">
                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            
            <button type="submit" class="btn btn-admin">
                <i class="fas fa-sign-in-alt me-2"></i>
                دخول لوحة التحكم
            </button>
        </form>
        
        <div class="mt-4 text-center">
            <small class="text-muted">
                للتجربة: اسم المستخدم: <strong>11</strong> | كلمة المرور: <strong>11</strong>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Admin login form
        document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Check admin credentials
            const adminCredentials = JSON.parse(localStorage.getItem('adminCredentials')) || {
                username: '11',
                password: '11'
            };
            
            if (username === adminCredentials.username && password === adminCredentials.password) {
                // Set admin session
                sessionStorage.setItem('adminLoggedIn', 'true');
                sessionStorage.setItem('adminLoginTime', new Date().getTime());
                
                // Show success message
                showAlert('تم تسجيل الدخول بنجاح!', 'success');
                
                // Redirect to admin dashboard
                setTimeout(() => {
                    window.location.href = 'admin-dashboard.html';
                }, 1500);
            } else {
                showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
            }
        });
        
        function showAlert(message, type) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} mt-3`;
            alertDiv.innerHTML = message;
            
            const form = document.getElementById('adminLoginForm');
            form.appendChild(alertDiv);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
        
        // Check if already logged in
        if (sessionStorage.getItem('adminLoggedIn') === 'true') {
            const loginTime = parseInt(sessionStorage.getItem('adminLoginTime'));
            const currentTime = new Date().getTime();
            const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
            
            if (currentTime - loginTime < sessionDuration) {
                window.location.href = 'admin-dashboard.html';
            } else {
                sessionStorage.removeItem('adminLoggedIn');
                sessionStorage.removeItem('adminLoginTime');
            }
        }
    </script>
</body>
</html>
