// Payment utility functions

// Calculate remaining amount by subtracting first payment and installments from total price
function calculateRemainingAmount(totalPrice, firstPayment, monthlyAmount, monthsCount, finalPayment = 0) {
    const totalMonthlyPayments = monthlyAmount * monthsCount;
    // First payment is now excluded from totalPayments calculation since it's handled separately
    const totalPayments = totalMonthlyPayments + finalPayment;
    return totalPrice - firstPayment - totalPayments;
}

// Format currency with Arabic numerals
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ').format(amount);
}

// Get month name in Arabic
function getArabicMonthName(date) {
    const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return arabicMonths[date.getMonth()];
}