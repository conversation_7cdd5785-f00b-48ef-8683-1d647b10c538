:root {
    --primary-color: #4a6b8a;
    --secondary-color: #146444;
    --accent-color: #6b4a8a;
    --light-color: #f8f5f2;
    --dark-color: #333333;
    --border-color: #d1c7b7;
    --success-color: #5a8a4a;
    --warning-color: #8a784a;
    --error-color: #8a4a5a;
}

body {
    font-family: 'Segoe UI', Tahoma, 'Arial', sans-serif;
    line-height: 1.8;
    color: var(--dark-color);
    background-color: var(--light-color);
    margin: 0;
    padding: 0;
}

.main-header {
    border-bottom: 5px solid var(--secondary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    padding: 2rem;
    height: 120px;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2em;
    color: white;
    align-items: center;
    justify-content: space-between;
}

.main-header h1 {
    margin: 0;
    font-size: 2rem;
}

.logo svg {
    fill: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    height: 80px;
    width: 80px;
    transform: scale(1.5);
}

.logo-left circle {
    stroke: 4px;
    stroke: white;
}

.logo-right rect {
    fill: var(--secondary-color);
}

main {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

section {
    background: white;
    border: 4px solid var(--border-color);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    transition: all 0.25s ease;
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 8px;
}

section::after {
    content: '';
    position: absolute;
    bottom: 0;
    height: 4px;
    width: 100%;
    background: linear-gradient();
}

section :is(h2, ol) {
    padding: 1rem;
    border-radius: 8px;
}

h2 {
    color: var(--primary-color);
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

select#exception {
    width: 8px;
    background: #f8f8f8;
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    width:100%;
    transition: all  0.3s ease;
}

select#exception:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.submit-button {
    display: none;
}

.submit-button.active {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin: 0.5rem;
    vertical-align: middle;
}

.submit-button.active:hover {
    background: var(--secondary-color);
}

#whatsapp-button {
    background-color: #25D366;
}

#whatsapp-button:hover {
    background-color: #1EAE54;
}

.form-group:last-child {
    margin-bottom: 0rem;
}

section:last-child {
    margin-bottom: 0 0 1rem;
}

.currency-input {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 1rem;
    color: #7f8c8d;
}

.radio-group {
    display: flex;
    gap: 1rem;
    background-color: #f8f8f8;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-top: 0.5rem;
    align-items: center;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: inherit;
    font-weight: normal;
    cursor: pointer;
    padding: 0.5rem;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.radio-group label:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.radio-group input[type="radio"]:checked + label {
    color: var(--primary-color);
    font-weight: bold;
    background-color: rgba(74, 107, 138, 0.1);
}

.notes-content {
    counter-reset: custom-counter;
    padding: 0;
}

.notes-content ol {
    padding: 0;
    margin: 1rem 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.notes-content li {
    position: relative;
    padding: 1rem;
    padding-right: 2.5rem;
    margin-bottom: 0;
    counter-increment: custom-counter;
    list-style-type: none;
    line-height: 1.8;
    border-bottom: 2px solid var(--light-color);
}

.notes-content li::before {
    content: counter(custom-counter) ".";
    position: absolute;
    right: 1rem;
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.notes-content li:not(:last-child) {
    border-bottom: 1px solid #eee;
}

section :is(ol, h2) {
    background: #fff;
    margin: 0;
    padding: 0;
    border-collapse: separate;
    border-spacing: 8px;
}

.print-controls {
    text-align: center;
    margin: 2rem auto;
    padding: 1rem;
    max-width:1200px;
}

.print-controls button {
    width: 100%;
    max-width: 200px;
}

@media (max-width: 768px) {
    .main-header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1em;
        padding: 1rem;
        height: auto;
    }

    .logo-left, .logo-right {
        margin: 0 auto;
    }

    .logo svg {
        height: 60px;
        width: 60px;
        transform: scale(1);
    }

    .main-header h1 {
        font-size: 1.5rem;
        margin: 0.5rem 0;
    }

    section {
        padding: 1rem;
        margin:  1rem 0;
    }

    .form-group {
        margin: 1rem;
    }

    .currency-input {
        flex-direction: column;
    }
    
    .currency-symbol {
        position: relative;
        margin: 0.5rem;
    }

    .print-controls button {
        width: calc(50% - 1rem);
        margin: 0.5rem;
    }
}

@media (max-width: 480px) {
    .main-header h1 {
        font-size: 1rem;
    }

    .radio-group {
        flex-direction: column;
    }

    .radio-group label {
        margin: 0.5rem;
    }

    .print-controls button {
        width: calc(100% - 1rem);
    }
}

/* Ensure forms stack vertically on mobile */
.form-group {
    grid-template-columns: 1fr;
}

/* Make tables responsive */
table {
    width: 100%;
    overflow: auto;
}

table th, table td {
    width: auto;
    margin: 0;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-collapse: collapse;
}

.notes-section {
    box-sizing: border-box;
    margin-bottom: 1.5rem;
}

.notes-section h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(138, 107, 74, 0.25);
}

.notes-section:first-of-type {
    margin-top: 2rem;
}

@media print {
    :root {
        --print-primary-color: #2c3e50;
        --print-secondary-color: #7f8c8d;
        --print-light-bg: #f8f9fa;
        --print-border-color: #dee2e6;
    }

    body {
        background-color: #fff;
        color: #000;
        font-size: 9pt; 
        line-height: 1.3; 
        font-family: 'Arial', sans-serif;
    }

    .container {
        width: 100%;
        min-height: auto;
        margin: 0;
        padding: 0; 
        box-shadow: none;
    }

    .main-header,
    .print-controls,
    .submit-button, 
    #back-to-form-button, 
    #preview-button, 
    #whatsapp-button, 
    #clear-button, 
    main > section:not(.summary-section), 
    .summary-section h2, 
    .notes-section { 
        display: none !important;
    }

    main {
        padding: 0;
        margin: 0;
    }

    .summary-section {
        display: block !important;
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important; 
        page-break-before: auto;
        width: 100%;
    }

    #summary-content {
        display: block !important; 
        padding: 0; 
        box-sizing: border-box;
        width: 100%;
    }

    .print-header {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 0.4cm; 
        margin-bottom: 0.5cm; 
        border-bottom: 1.5px solid var(--print-primary-color); 
        width: 100%;
        box-sizing: border-box;
    }

    .print-header-logo {
        display: block !important;
        flex-shrink: 0;
        width: 35px; 
        height: 35px;
    }
    .print-header-logo svg {
        width: 100%;
        height: 100%;
        display: block;
        fill: #333;
    }
    .print-header-logo.logo-left svg circle { stroke: #333; stroke-width: 10; fill: none; }
    .print-header-logo.logo-left svg text { fill: #000; font-size: 40px; font-family: sans-serif; }
    .print-header-logo.logo-right svg rect { fill: #ccc; }
    .print-header-logo.logo-right svg text { fill: #000; font-size: 40px; font-family: sans-serif; }

    .print-header-title {
        display: block !important;
        font-size: 13pt; 
        font-weight: bold;
        color: var(--print-primary-color);
        text-align: center;
        margin: 0;
        padding: 0;
        border: none;
        flex-grow: 1;
    }

    .summary-data-columns {
        column-count: 2;
        column-gap: 1cm; 
        column-fill: auto;
        margin-bottom: 0.5cm; 
    }

    .summary-section-group {
        break-inside: avoid-column; 
        margin-bottom: 0.5cm; 
        padding: 0.3cm; 
        border: 1px solid var(--print-border-color);
        border-radius: 3px; 
        background-color: var(--print-light-bg);
    }

    .summary-section-group h3 {
        font-size: 9.5pt; 
        color: var(--print-primary-color);
        border-bottom: 1px solid var(--print-secondary-color);
        padding-bottom: 0.1cm; 
        margin-top: 0;
        margin-bottom: 0.25cm; 
        font-weight: bold;
    }

    .summary-field {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.08cm 0; 
        margin-bottom: 0.05cm; 
        border-bottom: 1px dotted #ccc;
        font-size: 8pt; 
    }
    .summary-field:last-child { border-bottom: none; }

    .summary-field label {
        font-weight: bold;
        color: #333;
        margin-left: 0.2cm; 
        flex-shrink: 0;
        width: 80px; 
        text-align: right;
    }
    .summary-field span {
        text-align: right;
        word-break: break-word;
        color: #555;
        flex-grow: 1;
    }
    .summary-field span i { color: #999; font-style: italic; }

    .summary-field.notes {
        flex-direction: column;
        align-items: flex-start;
        border-bottom: none;
        margin-top: 0.2cm;
    }
    .summary-field.notes label {
        margin-bottom: 0.15cm;
        width: auto;
        border-bottom: 1px solid var(--print-border-color);
        padding-bottom: 0.1cm;
        color: var(--print-primary-color);
        font-size: 8.5pt;
        text-align: right;
        display: block;
        width: 100%;
        box-sizing: border-box;
    }
    .summary-field.notes span {
        text-align: right;
        white-space: pre-wrap;
        border: 1px solid var(--print-border-color);
        background-color: #fff;
        padding: 0.15cm; 
        width: 100%;
        box-sizing: border-box;
        min-height: 30px; 
        line-height: 1.3;
        font-size: 8pt;
        color: #333; 
    }

    .important-notes-print {
        display: block !important;
        margin-top: 0.4cm; 
        padding-top: 0.3cm; 
        border-top: 1px solid var(--print-secondary-color);
        column-span: all; 
        break-before: auto;
        page-break-inside: avoid; 
    }
    .important-notes-print h3 {
        font-size: 10pt; 
        color: var(--print-primary-color);
        margin-bottom: 0.25cm; 
        font-weight: bold;
        text-align: center;
    }
    .important-notes-print ol {
        padding-right: 1.5em; 
        margin: 0;
        list-style-type: decimal; 
        list-style-position: inside; 
        font-size: 8pt; 
        line-height: 1.3; 
        color: #333; 
    }
    .important-notes-print li {
        margin-bottom: 0.15cm; 
        padding-right: 0; 
        break-inside: avoid; 
        text-align: justify; 
    }
    .important-notes-print li::before {
        content: none !important; 
    }

    .signature-section-print {
        display: block !important;
        margin-top: 0.7cm; 
        padding-top: 0.3cm; 
        border-top: 1px solid var(--print-secondary-color);
        column-span: all; 
        break-before: auto; 
        page-break-inside: avoid; 
    }
    .signature-section-print h4 {
        font-size: 10pt; 
        color: var(--print-primary-color);
        margin-bottom: 0.4cm; 
        font-weight: bold;
        text-align: center;
    }
    .signature-boxes {
        display: flex;
        justify-content: space-around; 
        align-items: flex-end;
        flex-wrap: wrap; 
        gap: 0.5cm; 
    }
    .signature-box {
        width: calc(48% - 0.5cm); 
        min-width: 100px; 
        text-align: center;
        margin-bottom: 0.5cm; 
    }
    .signature-box label {
        display: block;
        font-size: 8.5pt; 
        font-weight: bold;
        margin-bottom: 0.8cm; 
        color: #333;
    }
    .signature-box span { 
        display: block;
        height: 1px;
        background-color: #888; 
        width: 75%; 
        margin: 0 auto; 
    }

    @page {
        size: A4;
        margin: 1.2cm; 
    }
}