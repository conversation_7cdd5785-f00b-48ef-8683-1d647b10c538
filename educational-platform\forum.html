<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منتدى النقاش - منصة التعلم الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعلم الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">الدورات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="forum.html">المنتدى</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="certificates.html">الشهادات</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">إنشاء حساب</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Forum Header -->
    <section class="forum-header bg-primary text-white py-5 mt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold">منتدى النقاش</h1>
                    <p class="lead">شارك أفكارك واطرح أسئلتك وتفاعل مع المجتمع التعليمي</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-warning btn-lg" data-bs-toggle="modal" data-bs-target="#newPostModal">
                        <i class="fas fa-plus me-2"></i>
                        موضوع جديد
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Forum Stats -->
    <section class="py-3 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <h4 class="text-primary">1,250</h4>
                        <small class="text-muted">عضو نشط</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h4 class="text-success">3,480</h4>
                        <small class="text-muted">موضوع</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h4 class="text-info">12,560</h4>
                        <small class="text-muted">مشاركة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h4 class="text-warning">156</h4>
                        <small class="text-muted">موضوع اليوم</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Forum Content -->
    <main class="py-5">
        <div class="container">
            <div class="row">
                <!-- Main Forum Content -->
                <div class="col-lg-8">
                    <!-- Forum Filters -->
                    <div class="forum-filters mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="filter" id="all-posts" checked>
                                    <label class="btn btn-outline-primary" for="all-posts">جميع المواضيع</label>
                                    
                                    <input type="radio" class="btn-check" name="filter" id="my-posts">
                                    <label class="btn btn-outline-primary" for="my-posts">مواضيعي</label>
                                    
                                    <input type="radio" class="btn-check" name="filter" id="unanswered">
                                    <label class="btn btn-outline-primary" for="unanswered">بدون إجابة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <select class="form-select">
                                    <option>الأحدث</option>
                                    <option>الأكثر نشاطاً</option>
                                    <option>الأكثر إعجاباً</option>
                                    <option>بدون إجابة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Forum Posts -->
                    <div class="forum-posts" id="forum-posts">
                        <!-- Posts will be loaded dynamically -->
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="صفحات المنتدى" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">السابق</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- Forum Sidebar -->
                <div class="col-lg-4">
                    <!-- Search -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">البحث في المنتدى</h6>
                        </div>
                        <div class="card-body">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="ابحث عن موضوع...">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Categories -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">التصنيفات</h6>
                        </div>
                        <div class="card-body">
                            <div class="category-list">
                                <a href="#" class="category-item d-flex justify-content-between align-items-center py-2 text-decoration-none">
                                    <span><i class="fas fa-code text-primary me-2"></i>البرمجة</span>
                                    <span class="badge bg-primary">245</span>
                                </a>
                                <a href="#" class="category-item d-flex justify-content-between align-items-center py-2 text-decoration-none">
                                    <span><i class="fas fa-paint-brush text-success me-2"></i>التصميم</span>
                                    <span class="badge bg-success">156</span>
                                </a>
                                <a href="#" class="category-item d-flex justify-content-between align-items-center py-2 text-decoration-none">
                                    <span><i class="fas fa-bullhorn text-warning me-2"></i>التسويق</span>
                                    <span class="badge bg-warning">89</span>
                                </a>
                                <a href="#" class="category-item d-flex justify-content-between align-items-center py-2 text-decoration-none">
                                    <span><i class="fas fa-users text-info me-2"></i>الإدارة</span>
                                    <span class="badge bg-info">67</span>
                                </a>
                                <a href="#" class="category-item d-flex justify-content-between align-items-center py-2 text-decoration-none">
                                    <span><i class="fas fa-question-circle text-secondary me-2"></i>أسئلة عامة</span>
                                    <span class="badge bg-secondary">123</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Top Contributors -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">أكثر المساهمين</h6>
                        </div>
                        <div class="card-body">
                            <div class="contributor-list">
                                <div class="contributor-item d-flex align-items-center py-2">
                                    <img src="https://via.placeholder.com/40x40/4285f4/ffffff?text=أ" 
                                         alt="أحمد محمد" class="rounded-circle me-3">
                                    <div class="flex-fill">
                                        <div class="fw-bold">أحمد محمد</div>
                                        <small class="text-muted">245 مشاركة</small>
                                    </div>
                                    <div class="badge bg-warning">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                </div>
                                <div class="contributor-item d-flex align-items-center py-2">
                                    <img src="https://via.placeholder.com/40x40/28a745/ffffff?text=ف" 
                                         alt="فاطمة أحمد" class="rounded-circle me-3">
                                    <div class="flex-fill">
                                        <div class="fw-bold">فاطمة أحمد</div>
                                        <small class="text-muted">198 مشاركة</small>
                                    </div>
                                </div>
                                <div class="contributor-item d-flex align-items-center py-2">
                                    <img src="https://via.placeholder.com/40x40/ffc107/333333?text=م" 
                                         alt="محمد علي" class="rounded-circle me-3">
                                    <div class="flex-fill">
                                        <div class="fw-bold">محمد علي</div>
                                        <small class="text-muted">167 مشاركة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">النشاط الأخير</h6>
                        </div>
                        <div class="card-body">
                            <div class="activity-list">
                                <div class="activity-item d-flex align-items-start py-2">
                                    <i class="fas fa-comment text-primary me-2 mt-1"></i>
                                    <div>
                                        <small class="text-muted">منذ 5 دقائق</small>
                                        <div class="small">علق <strong>أحمد</strong> على موضوع "تعلم JavaScript"</div>
                                    </div>
                                </div>
                                <div class="activity-item d-flex align-items-start py-2">
                                    <i class="fas fa-plus text-success me-2 mt-1"></i>
                                    <div>
                                        <small class="text-muted">منذ 15 دقيقة</small>
                                        <div class="small">أضاف <strong>سارة</strong> موضوع جديد في التصميم</div>
                                    </div>
                                </div>
                                <div class="activity-item d-flex align-items-start py-2">
                                    <i class="fas fa-heart text-danger me-2 mt-1"></i>
                                    <div>
                                        <small class="text-muted">منذ 30 دقيقة</small>
                                        <div class="small">أعجب <strong>محمد</strong> بموضوع "أفضل الممارسات"</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- New Post Modal -->
    <div class="modal fade" id="newPostModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء موضوع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="new-post-form">
                        <div class="mb-3">
                            <label for="post-category" class="form-label">التصنيف</label>
                            <select class="form-select" id="post-category" required>
                                <option value="">اختر التصنيف</option>
                                <option value="البرمجة">البرمجة</option>
                                <option value="التصميم">التصميم</option>
                                <option value="التسويق">التسويق</option>
                                <option value="الإدارة">الإدارة</option>
                                <option value="أسئلة عامة">أسئلة عامة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="post-title" class="form-label">عنوان الموضوع</label>
                            <input type="text" class="form-control" id="post-title" required 
                                   placeholder="اكتب عنواناً واضحاً ومفيداً">
                        </div>
                        <div class="mb-3">
                            <label for="post-content" class="form-label">محتوى الموضوع</label>
                            <textarea class="form-control" id="post-content" rows="8" required 
                                      placeholder="اكتب محتوى موضوعك هنا..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="post-tags" class="form-label">الكلمات المفتاحية</label>
                            <input type="text" class="form-control" id="post-tags" 
                                   placeholder="مثال: html, css, javascript (افصل بفاصلة)">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="createNewPost()">
                        <i class="fas fa-paper-plane me-2"></i>
                        نشر الموضوع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>منصة التعلم الإلكتروني</h5>
                    <p class="text-muted">منصة تعليمية متكاملة تهدف إلى توفير تعليم عالي الجودة للجميع</p>
                </div>
                <div class="col-md-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="courses.html" class="text-muted">الدورات</a></li>
                        <li><a href="forum.html" class="text-muted">المنتدى</a></li>
                        <li><a href="certificates.html" class="text-muted">الشهادات</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>تواصل معنا</h5>
                    <div class="social-links">
                        <a href="#" class="text-muted me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-instagram fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 منصة التعلم الإلكتروني. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data.js"></script>
    <script>
        // Load forum posts on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadForumPosts();
        });

        function loadForumPosts() {
            const container = document.getElementById('forum-posts');
            container.innerHTML = forumPosts.map(post => createForumPostHTML(post)).join('');
        }

        function createForumPostHTML(post) {
            return `
                <div class="forum-post">
                    <div class="post-header d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center">
                            <img src="${post.avatar}" alt="${post.author}" class="user-avatar me-3">
                            <div>
                                <h6 class="mb-1">${post.title}</h6>
                                <div class="post-meta">
                                    <span class="text-muted">بواسطة <strong>${post.author}</strong></span>
                                    <span class="text-muted mx-2">•</span>
                                    <span class="text-muted">${formatDate(post.date)}</span>
                                    <span class="text-muted mx-2">•</span>
                                    <span class="badge bg-primary">${post.category}</span>
                                </div>
                            </div>
                        </div>
                        <div class="post-actions">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="likePost(${post.id})">
                                <i class="fas fa-heart"></i> ${post.likes}
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="replyToPost(${post.id})">
                                <i class="fas fa-reply"></i> ${post.replies}
                            </button>
                        </div>
                    </div>
                    <div class="post-content">
                        <p>${post.content}</p>
                    </div>
                    <div class="post-footer d-flex justify-content-between align-items-center mt-3">
                        <div class="post-stats text-muted small">
                            <span><i class="fas fa-eye me-1"></i>125 مشاهدة</span>
                            <span class="mx-2">•</span>
                            <span><i class="fas fa-clock me-1"></i>آخر رد منذ ساعتين</span>
                        </div>
                        <div class="post-actions">
                            <button class="btn btn-sm btn-link text-muted" onclick="sharePost(${post.id})">
                                <i class="fas fa-share"></i> مشاركة
                            </button>
                            <button class="btn btn-sm btn-link text-muted" onclick="reportPost(${post.id})">
                                <i class="fas fa-flag"></i> إبلاغ
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function createNewPost() {
            const category = document.getElementById('post-category').value;
            const title = document.getElementById('post-title').value;
            const content = document.getElementById('post-content').value;
            const tags = document.getElementById('post-tags').value;

            if (!category || !title || !content) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // Create new post object
            const newPost = {
                id: Date.now(),
                title: title,
                content: content,
                author: 'المستخدم الحالي',
                avatar: 'https://via.placeholder.com/50x50/4285f4/ffffff?text=م',
                date: new Date().toISOString(),
                category: category,
                tags: tags.split(',').map(tag => tag.trim()),
                likes: 0,
                replies: 0
            };

            // Add to forum posts array
            forumPosts.unshift(newPost);

            // Reload posts
            loadForumPosts();

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('newPostModal'));
            modal.hide();
            document.getElementById('new-post-form').reset();

            // Show success message
            showAlert('تم نشر الموضوع بنجاح!', 'success');
        }

        function likePost(postId) {
            const post = forumPosts.find(p => p.id === postId);
            if (post) {
                post.likes++;
                loadForumPosts();
            }
        }

        function replyToPost(postId) {
            // This would open a reply modal or navigate to post details
            alert('سيتم فتح صفحة الرد على الموضوع');
        }

        function sharePost(postId) {
            // Copy post URL to clipboard
            const url = `${window.location.origin}/forum-post.html?id=${postId}`;
            navigator.clipboard.writeText(url).then(() => {
                showAlert('تم نسخ رابط الموضوع', 'success');
            });
        }

        function reportPost(postId) {
            if (confirm('هل تريد الإبلاغ عن هذا الموضوع؟')) {
                showAlert('تم إرسال البلاغ. شكراً لك', 'info');
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) {
                return 'منذ يوم';
            } else if (diffDays < 7) {
                return `منذ ${diffDays} أيام`;
            } else {
                return date.toLocaleDateString('ar-SA');
            }
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 100px; left: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto dismiss after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
