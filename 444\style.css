.print-controls button {
    text-align: center;
    margin: 2rem auto;
    padding: 1rem;
    max-width:1200px;
}

.print-controls button {
    width: 100%;
    max-width: 200px;
    font-size: 1.2rem;
}

@media (max-width: 768px) {

.summary-field label {
    font-weight: bold;
    color: #333;
    margin-left: 0.2cm;
    flex-shrink: 0;
    flex-basis: 80px; 
    text-align: right;
    display: inline-block;
    border: 1px solid #ccc; 
    padding: 0.05cm 0.1cm; 
    background-color: #eee; 
    border-radius: 2px;
}
.summary-field span {
    text-align: right;
    word-break: break-word;
    color: #444; 
    flex-grow: 1;
    border: 1px solid #ccc; 
    padding: 0.05cm 0.1cm; 
    background-color: #fff; 
    border-radius: 2px;
    min-height: 1.3em; 
    display: inline-block;
}

.print-header-logo {
    display: flex !important; 
    flex-shrink: 0; 
    width: 35px; 
    height: 35px; 
}
.print-header-logo svg { 
    width: 100%;
    height: 100%;
    display: block; 
    fill: #333; 
}
.print-header-logo.logo-left svg circle {
    stroke: #333; 
    stroke-width: 10; 

.print-header-logo {
    display: flex !important; 
    flex-shrink: 0; 
    width: 50px; 
    height: 50px; 
}
.print-header-logo svg { 
    width: 100%;
    height: 100%;
    display: block; 
    fill: #333; 
}
.print-header-logo.logo-left svg circle {
    stroke: #333; 
    stroke-width: 6; 
}

.summary-section-group {
    break-inside: avoid-column; 
    margin-bottom: 0.5cm;
    padding: 0.3cm;
    border: 1px solid var(--print-border-color); 
    border-radius: 3px;
    background-color: #f2f2f2;
    box-shadow: none; 
}

.summary-section-group h3 {