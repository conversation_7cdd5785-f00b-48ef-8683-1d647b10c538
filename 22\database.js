document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const bookingsTableBody = document.getElementById('bookingsTableBody');
    const emptyMessage = document.getElementById('emptyMessage');
    const exportBtn = document.getElementById('exportBtn');
    const clearDbBtn = document.getElementById('clearDbBtn');
    const pagination = document.getElementById('pagination');
    
    // Modals
    const bookingDetailsModal = new bootstrap.Modal(document.getElementById('bookingDetailsModal'));
    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    
    // Variables
    let bookings = [];
    let filteredBookings = [];
    let currentPage = 1;
    const bookingsPerPage = 10;
    let selectedBookingNumber = null;
    
    // Load bookings on page load
    loadBookings();
    
    // Event Listeners
    searchBtn.addEventListener('click', searchBookings);
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            searchBookings();
        }
    });
    
    exportBtn.addEventListener('click', exportBookings);
    clearDbBtn.addEventListener('click', function() {
        if (confirm('هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            clearDatabase();
        }
    });
    
    document.getElementById('printReceiptBtn').addEventListener('click', function() {
        const bookingNumber = document.getElementById('detailsBookingNumber').textContent;
        window.open(`receipt.html?id=${bookingNumber}`, '_blank');
    });
    
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        deleteBooking(selectedBookingNumber);
        deleteConfirmModal.hide();
    });
    
    // Functions
    function loadBookings() {
        // Get bookings from localStorage
        const storedBookings = localStorage.getItem('bookings');
        bookings = storedBookings ? JSON.parse(storedBookings) : [];
        filteredBookings = [...bookings];
        
        // Update UI
        updateBookingsTable();
    }
    
    function updateBookingsTable() {
        if (filteredBookings.length === 0) {
            // Show empty message
            emptyMessage.classList.remove('d-none');
            bookingsTableBody.innerHTML = '';
            pagination.innerHTML = '';
            return;
        }
        
        // Hide empty message
        emptyMessage.classList.add('d-none');
        
        // Calculate pagination
        const totalPages = Math.ceil(filteredBookings.length / bookingsPerPage);
        if (currentPage > totalPages) {
            currentPage = totalPages;
        }
        
        const startIndex = (currentPage - 1) * bookingsPerPage;
        const endIndex = Math.min(startIndex + bookingsPerPage, filteredBookings.length);
        const pageBookings = filteredBookings.slice(startIndex, endIndex);
        
        // Clear existing rows
        bookingsTableBody.innerHTML = '';
        
        // Add booking rows
        pageBookings.forEach(booking => {
            const row = createBookingRow(booking);
            bookingsTableBody.appendChild(row);
        });
        
        // Update pagination
        updatePagination(totalPages);
    }
    
    function createBookingRow(booking) {
        const row = document.createElement('tr');
        
        // Format date in DD-MM-YYYY format
        const bookingDate = new Date(booking.bookingDate);
        const day = bookingDate.getDate().toString().padStart(2, '0');
        const month = (bookingDate.getMonth() + 1).toString().padStart(2, '0');
        const year = bookingDate.getFullYear();
        const formattedDate = `${day}-${month}-${year}`;
        
        // Create cells
        row.innerHTML = `
            <td>${booking.bookingNumber}</td>
            <td>${booking.fullName} ${booking.lastName || ''}</td>
            <td>${booking.idNumber}</td>
            <td>${booking.phone || '-'}</td>
            <td>${booking.areaName || '-'}</td>
            <td>${booking.unitNumber || '-'}</td>
            <td>${formattedDate}</td>
            <td>
                <div class="action-btn-group">
                    <button class="btn btn-sm btn-primary view-details" data-booking="${booking.bookingNumber}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success print-receipt" data-booking="${booking.bookingNumber}">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="btn btn-sm btn-info monthly-payment" data-booking="${booking.bookingNumber}">
                        <i class="fas fa-calendar-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-booking" data-booking="${booking.bookingNumber}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        
        // Add event listeners
        row.querySelector('.view-details').addEventListener('click', function() {
            showBookingDetails(booking.bookingNumber);
        });
        
        row.querySelector('.print-receipt').addEventListener('click', function() {
            window.open(`receipt.html?id=${booking.bookingNumber}`, '_blank');
        });
        
        row.querySelector('.monthly-payment').addEventListener('click', function() {
            window.location.href = `monthly_payment.html?id=${booking.bookingNumber}`;
        });
        
        row.querySelector('.delete-booking').addEventListener('click', function() {
            confirmDeleteBooking(booking.bookingNumber);
        });
        
        return row;
    }
    
    function updatePagination(totalPages) {
        pagination.innerHTML = '';
        
        if (totalPages <= 1) {
            return;
        }
        
        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>`;
        prevLi.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                updateBookingsTable();
            }
        });
        pagination.appendChild(prevLi);
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
            pageLi.addEventListener('click', function(e) {
                e.preventDefault();
                currentPage = i;
                updateBookingsTable();
            });
            pagination.appendChild(pageLi);
        }
        
        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>`;
        nextLi.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                currentPage++;
                updateBookingsTable();
            }
        });
        pagination.appendChild(nextLi);
    }
    
    function searchBookings() {
        const searchTerm = searchInput.value.trim().toLowerCase();
        
        if (!searchTerm) {
            filteredBookings = [...bookings];
        } else {
            filteredBookings = bookings.filter(booking => {
                return (
                    booking.fullName.toLowerCase().includes(searchTerm) ||
                    booking.idNumber.toLowerCase().includes(searchTerm) ||
                    booking.bookingNumber.toLowerCase().includes(searchTerm) ||
                    (booking.phone && booking.phone.toLowerCase().includes(searchTerm))
                );
            });
        }
        
        currentPage = 1;
        updateBookingsTable();
    }
    
    function showBookingDetails(bookingNumber) {
        const booking = bookings.find(b => b.bookingNumber === bookingNumber);
        
        if (!booking) {
            alert('لم يتم العثور على الحجز');
            return;
        }
        
        // Format date in DD-MM-YYYY format
        const bookingDate = new Date(booking.bookingDate);
        const day = bookingDate.getDate().toString().padStart(2, '0');
        const month = (bookingDate.getMonth() + 1).toString().padStart(2, '0');
        const year = bookingDate.getFullYear();
        const formattedDate = `${day}-${month}-${year}`;
        
        // Populate modal content
        const modalContent = document.getElementById('bookingDetailsContent');
        
        modalContent.innerHTML = `
            <h5 class="border-bottom pb-2 mb-3">معلومات الزبون</h5>
            <div class="row mb-4">
                <div class="col-md-6">
                    <p><strong>الاسم الكامل:</strong> ${booking.fullName} ${booking.lastName || ''}</p>
                    <p><strong>رقم الهوية:</strong> <span id="detailsBookingNumber">${booking.bookingNumber}</span></p>
                    <p><strong>رقم الهاتف الأول:</strong> ${booking.phone || '-'}</p>
                    <p><strong>رقم الهاتف الثاني:</strong> ${booking.secondPhone || '-'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>اسم الأم الثلاثي:</strong> ${booking.motherName || '-'}</p>
                    <p><strong>رقم بطاقة السكن:</strong> ${booking.residenceCardNumber || '-'}</p>
                    <p><strong>مصدر الزبون:</strong> ${booking.customerSource || '-'}</p>
                    <p><strong>تاريخ الحجز:</strong> ${formattedDate || '-'}</p>
                </div>
            </div>
            
            <h5 class="border-bottom pb-2 mb-3">العنوان الكامل</h5>
            <div class="mb-4">
                <p>${booking.fullAddress || '-'}</p>
            </div>
            
            <h5 class="border-bottom pb-2 mb-3">معلومات الوحدة السكنية</h5>
            <div class="row mb-4">
                <div class="col-md-6">
                    <p><strong>اسم المنطقة:</strong> ${booking.areaName || '-'}</p>
                    <p><strong>رقم المبنى:</strong> ${booking.buildingNumber || '-'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>رقم الوحدة:</strong> ${booking.unitNumber || '-'}</p>
                    <p><strong>المساحة الاجمالية:</strong> ${booking.totalArea || '-'} م²</p>
                    <p><strong>المساحة الفعلية:</strong> ${booking.actualArea || '-'} م²</p>
                    <p><strong>تصنيف الوحدة:</strong> ${booking.unitClassification || '-'}</p>
                </div>
            </div>
            
            <h5 class="border-bottom pb-2 mb-3">معلومات الدفع</h5>
            <div class="row mb-4">
                <div class="col-md-6">
                    <p><strong>طريقة التسديد:</strong> ${booking.paymentMethod || '-'}</p>
                    <p><strong>استثناء:</strong> ${booking.exemption || 'لا'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>الدفعة الأولى:</strong> ${booking.firstPayment || '-'} دينار</p>
                    <p><strong>سعر الوحدة:</strong> ${booking.unitPrice || '-'} دينار</p>
                    <p><a href="payment.html?id=${booking.bookingNumber}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-money-bill-wave me-1"></i> دفع الأقساط
                    </a></p>
                </div>
            </div>
            
            <h5 class="border-bottom pb-2 mb-3">ملاحظات إضافية</h5>
            <div class="mb-4">
                <p>${booking.notes || 'لا توجد ملاحظات إضافية'}</p>
            </div>
        `;
        
        // Show modal
        bookingDetailsModal.show();
    }
    
    function confirmDeleteBooking(bookingNumber) {
        selectedBookingNumber = bookingNumber;
        document.getElementById('deleteBookingNumber').textContent = bookingNumber;
        deleteConfirmModal.show();
    }
    
    function deleteBooking(bookingNumber) {
        // Filter out the booking to delete
        bookings = bookings.filter(booking => booking.bookingNumber !== bookingNumber);
        
        // Update localStorage
        localStorage.setItem('bookings', JSON.stringify(bookings));
        
        // Update filtered bookings
        filteredBookings = filteredBookings.filter(booking => booking.bookingNumber !== bookingNumber);
        
        // Update UI
        updateBookingsTable();
        
        // Show notification if available
        if (window.showNotification) {
            window.showNotification('تم حذف الحجز بنجاح', 'success', 3000);
        }
    }
    
    function exportBookings() {
        if (bookings.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }
        
        // Prepare CSV content
        let csvContent = 'data:text/csv;charset=utf-8,\uFEFF'; // Add BOM for proper Arabic display
        
        // Add headers
        const headers = [
            'رقم الحجز',
            'الاسم الكامل',
            'رقم الهوية',
            'رقم الهاتف',
            'اسم المنطقة',
            'رقم الوحدة',
            'رقم المبنى',
            'تصنيف الوحدة',
            'المساحة الاجمالية',
            'سعر الوحدة',
            'تاريخ الحجز'
        ];
        csvContent += headers.join(',') + '\n';
        
        // Add data rows
        bookings.forEach(booking => {
            const row = [
                `"${booking.bookingNumber || ''}"`,
                `"${booking.fullName || ''} ${booking.lastName || ''}"`,
                `"${booking.idNumber || ''}"`,
                `"${booking.phone || ''}"`,
                `"${booking.areaName || ''}"`,
                `"${booking.unitNumber || ''}"`,
                `"${booking.buildingNumber || ''}"`,
                `"${booking.unitClassification || ''}"`,
                `"${booking.totalArea || ''}"`,
                `"${booking.unitPrice || ''}"`,
                `"${new Date(booking.bookingDate).toLocaleDateString('ar-SA') || ''}"`
            ];
            csvContent += row.join(',') + '\n';
        });
        
        // Create download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `حجوزات_مجمع_المؤمل_${new Date().toLocaleDateString('ar-SA')}.csv`);
        document.body.appendChild(link);
        
        // Trigger download
        link.click();
        
        // Clean up
        document.body.removeChild(link);
    }
    
    function clearDatabase() {
        // Clear bookings
        bookings = [];
        filteredBookings = [];
        
        // Update localStorage
        localStorage.setItem('bookings', JSON.stringify(bookings));
        
        // Update UI
        updateBookingsTable();
        
        // Show notification if available
        if (window.showNotification) {
            window.showNotification('تم مسح جميع البيانات بنجاح', 'success', 3000);
        }
    }
});