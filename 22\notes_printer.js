document.addEventListener('DOMContentLoaded', function() {
    // Get the print notes button
    const printNotesBtn = document.getElementById('printNotesBtn');
    
    if (printNotesBtn) {
        printNotesBtn.addEventListener('click', function() {
            printImportantNotes();
        });
    }
    
    function printImportantNotes() {
        // Get the important notes content
        const notesList = document.querySelector('.important-notes-list');
        
        if (!notesList) {
            console.error('Important notes list not found');
            return;
        }
        
        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        
        // Write the content to the new window
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>الملاحظات الهامة - مجمع المؤمل السكني</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                    
                    body {
                        font-family: 'Tajawal', sans-serif;
                        margin: 0;
                        padding: 20px;
                        line-height: 1.4;
                    }
                    
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                    }
                    
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    
                    .logo {
                        width: 80px;
                        height: 80px;
                        margin: 0 auto 10px;
                    }
                    
                    h1 {
                        color: #1a6e93;
                        margin: 10px 0;
                        font-size: 24px;
                    }
                    
                    h2 {
                        color: #1a6e93;
                        border-bottom: 2px solid #1a6e93;
                        padding-bottom: 5px;
                        margin-bottom: 15px;
                        font-size: 18px;
                    }
                    
                    ul {
                        padding-right: 20px;
                    }
                    
                    li {
                        margin-bottom: 8px;
                        font-size: 14px;
                    }
                    
                    .committees {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #dee2e6;
                    }
                    
                    .committee {
                        text-align: center;
                        width: 20%;
                    }
                    
                    .signature-line {
                        width: 100%;
                        border-top: 1px solid #000;
                        margin-top: 40px;
                    }
                    
                    .committee-title {
                        font-size: 14px;
                        margin-top: 5px;
                        font-weight: bold;
                    }
                    
                    .footer {
                        text-align: center;
                        margin-top: 20px;
                        font-size: 12px;
                        color: #6c757d;
                    }
                    
                    @media print {
                        body {
                            padding: 0;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">
                            <svg width="80" height="80" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="#1a6e93" />
                                <rect x="30" y="25" width="15" height="50" fill="white" />
                                <rect x="55" y="25" width="15" height="50" fill="white" />
                                <rect x="30" y="25" width="40" height="15" fill="white" />
                            </svg>
                        </div>
                        <h1>مجمع المؤمل السكني</h1>
                    </div>
                    
                    <h2>الملاحظات الهامة</h2>
                    <ul>
                        ${notesList.innerHTML}
                    </ul>
                    
                    <div class="committees">
                        <div class="committee">
                            <div class="signature-line"></div>
                            <div class="committee-title">اللجنة المالية</div>
                        </div>
                        <div class="committee">
                            <div class="signature-line"></div>
                            <div class="committee-title">لجنة المبيعات</div>
                        </div>
                        <div class="committee">
                            <div class="signature-line"></div>
                            <div class="committee-title">لجنة التدقيق</div>
                        </div>
                        <div class="committee">
                            <div class="signature-line"></div>
                            <div class="committee-title">المدير التنفيذي</div>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <p>للاستفسار: 123-456-7890 | <EMAIL></p>
                        <p>جميع الحقوق محفوظة &copy; مجمع المؤمل السكني ${new Date().getFullYear()}</p>
                    </div>
                </div>
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);
        
        // Close the document stream
        printWindow.document.close();
    }
});