// Admin Dashboard JavaScript

// Check authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    initializeDashboard();
    loadSiteSettings();
    initializeChart();
});

// Authentication check
function checkAdminAuth() {
    const isLoggedIn = sessionStorage.getItem('adminLoggedIn');
    const loginTime = parseInt(sessionStorage.getItem('adminLoginTime'));
    const currentTime = new Date().getTime();
    const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours

    if (!isLoggedIn || (currentTime - loginTime > sessionDuration)) {
        window.location.href = 'admin-login.html';
        return;
    }
}

// Initialize dashboard
function initializeDashboard() {
    // Sidebar navigation
    const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
    const sections = document.querySelectorAll('.section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSection = this.getAttribute('data-section');
            switchSection(targetSection);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Update page title
            const title = this.querySelector('span').textContent;
            document.getElementById('pageTitle').textContent = title;
        });
    });
    
    // Mobile sidebar toggle
    const toggleSidebar = document.getElementById('toggleSidebar');
    const closeSidebar = document.getElementById('closeSidebar');
    const sidebar = document.getElementById('sidebar');
    
    if (toggleSidebar) {
        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.add('show');
        });
    }
    
    if (closeSidebar) {
        closeSidebar.addEventListener('click', () => {
            sidebar.classList.remove('show');
        });
    }
    
    // Site settings form
    const siteSettingsForm = document.getElementById('siteSettingsForm');
    if (siteSettingsForm) {
        siteSettingsForm.addEventListener('submit', saveSiteSettings);
    }
    
    // Color inputs for theme customizer
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('change', updateThemePreview);
    });
    
    // Font selector
    const fontSelector = document.getElementById('siteFont');
    if (fontSelector) {
        fontSelector.addEventListener('change', updateThemePreview);
    }
}

// Switch between sections
function switchSection(sectionId) {
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
}

// Load site settings
function loadSiteSettings() {
    const settings = JSON.parse(localStorage.getItem('siteSettings')) || {
        siteName: 'منصة التعلم الإلكتروني',
        siteDescription: 'منصة تعليمية متكاملة',
        siteEmail: '<EMAIL>',
        sitePhone: '+966 50 123 4567',
        siteAddress: 'الرياض، المملكة العربية السعودية'
    };
    
    // Populate form fields
    Object.keys(settings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.value = settings[key];
        }
    });
}

// Save site settings
function saveSiteSettings(e) {
    e.preventDefault();
    
    const settings = {
        siteName: document.getElementById('siteName').value,
        siteDescription: document.getElementById('siteDescription').value,
        siteEmail: document.getElementById('siteEmail').value,
        sitePhone: document.getElementById('sitePhone').value,
        siteAddress: document.getElementById('siteAddress').value
    };
    
    localStorage.setItem('siteSettings', JSON.stringify(settings));
    
    // Update main site
    updateMainSite(settings);
    
    showAlert('تم حفظ الإعدادات بنجاح!', 'success');
}

// Update main site with new settings
function updateMainSite(settings) {
    // This would typically send data to a server
    // For demo purposes, we'll update localStorage
    localStorage.setItem('currentSiteSettings', JSON.stringify(settings));
}

// Theme customizer functions
function updateThemePreview() {
    const primaryColor = document.getElementById('primaryColor').value;
    const secondaryColor = document.getElementById('secondaryColor').value;
    const successColor = document.getElementById('successColor').value;
    const warningColor = document.getElementById('warningColor').value;
    const siteFont = document.getElementById('siteFont').value;
    
    const preview = document.getElementById('themePreview');
    if (preview) {
        preview.style.fontFamily = siteFont;
        
        const previewHeader = preview.querySelector('.preview-header');
        const previewHero = preview.querySelector('.preview-hero');
        
        if (previewHeader) {
            previewHeader.style.background = primaryColor;
        }
        
        if (previewHero) {
            previewHero.style.background = `linear-gradient(45deg, ${primaryColor}, ${secondaryColor})`;
        }
    }
}

function applyTheme() {
    const theme = {
        primaryColor: document.getElementById('primaryColor').value,
        secondaryColor: document.getElementById('secondaryColor').value,
        successColor: document.getElementById('successColor').value,
        warningColor: document.getElementById('warningColor').value,
        siteFont: document.getElementById('siteFont').value
    };
    
    // Save theme to localStorage
    localStorage.setItem('customTheme', JSON.stringify(theme));
    
    // Apply theme to current page
    applyThemeToPage(theme);
    
    showAlert('تم تطبيق التصميم بنجاح!', 'success');
}

function applyThemeToPage(theme) {
    const root = document.documentElement;
    root.style.setProperty('--admin-primary', theme.primaryColor);
    root.style.setProperty('--admin-secondary', theme.secondaryColor);
    root.style.setProperty('--admin-success', theme.successColor);
    root.style.setProperty('--admin-warning', theme.warningColor);
    document.body.style.fontFamily = theme.siteFont;
}

function resetTheme() {
    // Reset to default values
    document.getElementById('primaryColor').value = '#667eea';
    document.getElementById('secondaryColor').value = '#764ba2';
    document.getElementById('successColor').value = '#28a745';
    document.getElementById('warningColor').value = '#ffc107';
    document.getElementById('siteFont').value = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    
    // Remove custom theme
    localStorage.removeItem('customTheme');
    
    // Reset CSS variables
    const root = document.documentElement;
    root.style.setProperty('--admin-primary', '#667eea');
    root.style.setProperty('--admin-secondary', '#764ba2');
    root.style.setProperty('--admin-success', '#28a745');
    root.style.setProperty('--admin-warning', '#ffc107');
    document.body.style.fontFamily = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    
    updateThemePreview();
    showAlert('تم إعادة تعيين التصميم!', 'info');
}

// Content management functions
function showContentEditor(section) {
    const editor = document.getElementById('contentEditor');
    
    let content = '';
    switch(section) {
        case 'hero':
            content = `
                <div class="content-editor">
                    <div class="editor-toolbar">
                        <strong>تحرير محتوى الصفحة الرئيسية</strong>
                    </div>
                    <div class="editor-content">
                        <div class="mb-3">
                            <label class="form-label">العنوان الرئيسي</label>
                            <input type="text" class="form-control" id="heroTitle" value="تعلم مهارات المستقبل">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">النص الفرعي</label>
                            <textarea class="form-control" id="heroSubtitle" rows="3">انضم إلى آلاف الطلاب واكتسب مهارات جديدة في مختلف المجالات</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نص الزر الأول</label>
                            <input type="text" class="form-control" id="heroButton1" value="ابدأ التعلم الآن">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نص الزر الثاني</label>
                            <input type="text" class="form-control" id="heroButton2" value="إنشاء حساب مجاني">
                        </div>
                        <button class="btn btn-primary" onclick="saveContent('hero')">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </div>
            `;
            break;
        case 'courses':
            content = `
                <div class="content-editor">
                    <div class="editor-toolbar">
                        <strong>تحرير محتوى قسم الدورات</strong>
                    </div>
                    <div class="editor-content">
                        <div class="mb-3">
                            <label class="form-label">عنوان القسم</label>
                            <input type="text" class="form-control" id="coursesTitle" value="الدورات الأكثر شعبية">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف القسم</label>
                            <textarea class="form-control" id="coursesDescription" rows="2">اكتشف أفضل الدورات التعليمية المختارة بعناية</textarea>
                        </div>
                        <button class="btn btn-primary" onclick="saveContent('courses')">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </div>
            `;
            break;
        case 'about':
            content = `
                <div class="content-editor">
                    <div class="editor-toolbar">
                        <strong>تحرير محتوى صفحة من نحن</strong>
                    </div>
                    <div class="editor-content">
                        <div class="mb-3">
                            <label class="form-label">نبذة عن المنصة</label>
                            <textarea class="form-control" id="aboutContent" rows="6">نحن منصة تعليمية رائدة تهدف إلى توفير تعليم عالي الجودة للجميع. نقدم دورات متنوعة في مختلف المجالات مع أفضل المدرسين والخبراء.</textarea>
                        </div>
                        <button class="btn btn-primary" onclick="saveContent('about')">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </div>
            `;
            break;
    }
    
    editor.innerHTML = content;
}

function saveContent(section) {
    let contentData = {};
    
    switch(section) {
        case 'hero':
            contentData = {
                title: document.getElementById('heroTitle').value,
                subtitle: document.getElementById('heroSubtitle').value,
                button1: document.getElementById('heroButton1').value,
                button2: document.getElementById('heroButton2').value
            };
            break;
        case 'courses':
            contentData = {
                title: document.getElementById('coursesTitle').value,
                description: document.getElementById('coursesDescription').value
            };
            break;
        case 'about':
            contentData = {
                content: document.getElementById('aboutContent').value
            };
            break;
    }
    
    localStorage.setItem(`content_${section}`, JSON.stringify(contentData));
    showAlert('تم حفظ المحتوى بنجاح!', 'success');
}

// Initialize chart
function initializeChart() {
    const ctx = document.getElementById('visitorsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الزوار',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Utility functions
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function logout() {
    sessionStorage.removeItem('adminLoggedIn');
    sessionStorage.removeItem('adminLoginTime');
    window.location.href = 'admin-login.html';
}

// Media Manager Functions
function initializeMediaManager() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');

    if (uploadArea && fileInput) {
        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFileUpload(files);
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            handleFileUpload(e.target.files);
        });
    }

    loadMediaFiles();
}

function handleFileUpload(files) {
    Array.from(files).forEach(file => {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            showAlert('حجم الملف كبير جداً. الحد الأقصى 5MB', 'danger');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const fileData = {
                id: Date.now() + Math.random(),
                name: file.name,
                type: file.type,
                size: file.size,
                data: e.target.result,
                uploadDate: new Date().toISOString()
            };

            saveMediaFile(fileData);
            addMediaToGrid(fileData);
        };
        reader.readAsDataURL(file);
    });
}

function saveMediaFile(fileData) {
    const mediaFiles = JSON.parse(localStorage.getItem('mediaFiles')) || [];
    mediaFiles.push(fileData);
    localStorage.setItem('mediaFiles', JSON.stringify(mediaFiles));
}

function loadMediaFiles() {
    const mediaFiles = JSON.parse(localStorage.getItem('mediaFiles')) || [];
    const mediaGrid = document.getElementById('mediaGrid');

    if (mediaGrid) {
        mediaGrid.innerHTML = '';
        mediaFiles.forEach(file => {
            addMediaToGrid(file);
        });
    }
}

function addMediaToGrid(fileData) {
    const mediaGrid = document.getElementById('mediaGrid');
    if (!mediaGrid) return;

    const mediaItem = document.createElement('div');
    mediaItem.className = 'media-item';
    mediaItem.onclick = () => showFileInfo(fileData);

    const isImage = fileData.type.startsWith('image/');
    const preview = isImage ?
        `<img src="${fileData.data}" alt="${fileData.name}">` :
        `<i class="fas fa-file fa-3x text-muted"></i>`;

    mediaItem.innerHTML = `
        ${preview}
        <div class="media-name">${fileData.name}</div>
        <div class="media-actions">
            <button class="btn btn-sm btn-outline-danger" onclick="deleteMediaFile('${fileData.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    mediaGrid.appendChild(mediaItem);
}

function showFileInfo(fileData) {
    const fileInfo = document.getElementById('fileInfo');
    if (!fileInfo) return;

    const sizeInKB = (fileData.size / 1024).toFixed(2);
    const uploadDate = new Date(fileData.uploadDate).toLocaleDateString('ar-SA');

    fileInfo.innerHTML = `
        <div class="file-details">
            <h6>${fileData.name}</h6>
            <p><strong>النوع:</strong> ${fileData.type}</p>
            <p><strong>الحجم:</strong> ${sizeInKB} KB</p>
            <p><strong>تاريخ الرفع:</strong> ${uploadDate}</p>
            <div class="mt-3">
                <button class="btn btn-sm btn-primary" onclick="copyFileUrl('${fileData.data}')">
                    <i class="fas fa-copy me-2"></i>نسخ الرابط
                </button>
                <button class="btn btn-sm btn-success" onclick="downloadFile('${fileData.data}', '${fileData.name}')">
                    <i class="fas fa-download me-2"></i>تحميل
                </button>
            </div>
        </div>
    `;
}

function copyFileUrl(dataUrl) {
    navigator.clipboard.writeText(dataUrl).then(() => {
        showAlert('تم نسخ رابط الملف!', 'success');
    });
}

function downloadFile(dataUrl, filename) {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = filename;
    link.click();
}

function deleteMediaFile(fileId) {
    if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
        const mediaFiles = JSON.parse(localStorage.getItem('mediaFiles')) || [];
        const updatedFiles = mediaFiles.filter(file => file.id != fileId);
        localStorage.setItem('mediaFiles', JSON.stringify(updatedFiles));
        loadMediaFiles();
        showAlert('تم حذف الملف!', 'success');
    }
}

// Users Management Functions
function loadUsers() {
    const users = JSON.parse(localStorage.getItem('siteUsers')) || [
        {
            id: 1,
            name: 'محمد أحمد',
            email: '<EMAIL>',
            type: 'student',
            registrationDate: '2024-01-15',
            status: 'active'
        },
        {
            id: 2,
            name: 'فاطمة خالد',
            email: '<EMAIL>',
            type: 'teacher',
            registrationDate: '2024-01-10',
            status: 'active'
        }
    ];

    const usersTable = document.getElementById('usersTable');
    if (!usersTable) return;

    usersTable.innerHTML = users.map(user => `
        <tr>
            <td>${user.name}</td>
            <td>${user.email}</td>
            <td>
                <span class="badge bg-${user.type === 'admin' ? 'danger' : user.type === 'teacher' ? 'warning' : 'primary'}">
                    ${user.type === 'admin' ? 'مدير' : user.type === 'teacher' ? 'مدرس' : 'طالب'}
                </span>
            </td>
            <td>${new Date(user.registrationDate).toLocaleDateString('ar-SA')}</td>
            <td>
                <span class="badge bg-${user.status === 'active' ? 'success' : 'secondary'}">
                    ${user.status === 'active' ? 'نشط' : 'معطل'}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser(${user.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function addUser() {
    const name = document.getElementById('userName').value;
    const email = document.getElementById('userEmail').value;
    const type = document.getElementById('userType').value;
    const password = document.getElementById('userPassword').value;

    if (!name || !email || !type || !password) {
        showAlert('يرجى ملء جميع الحقول', 'danger');
        return;
    }

    const users = JSON.parse(localStorage.getItem('siteUsers')) || [];
    const newUser = {
        id: Date.now(),
        name,
        email,
        type,
        password,
        registrationDate: new Date().toISOString(),
        status: 'active'
    };

    users.push(newUser);
    localStorage.setItem('siteUsers', JSON.stringify(users));

    // Close modal and refresh table
    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
    modal.hide();
    document.getElementById('addUserForm').reset();
    loadUsers();

    showAlert('تم إضافة المستخدم بنجاح!', 'success');
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        const users = JSON.parse(localStorage.getItem('siteUsers')) || [];
        const updatedUsers = users.filter(user => user.id !== userId);
        localStorage.setItem('siteUsers', JSON.stringify(updatedUsers));
        loadUsers();
        showAlert('تم حذف المستخدم!', 'success');
    }
}

// Security Functions
function initializeSecurity() {
    const adminCredentialsForm = document.getElementById('adminCredentialsForm');
    if (adminCredentialsForm) {
        adminCredentialsForm.addEventListener('submit', updateAdminCredentials);
    }
}

function updateAdminCredentials(e) {
    e.preventDefault();

    const newUsername = document.getElementById('newUsername').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (newPassword !== confirmPassword) {
        showAlert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'danger');
        return;
    }

    const adminCredentials = {
        username: newUsername,
        password: newPassword
    };

    localStorage.setItem('adminCredentials', JSON.stringify(adminCredentials));
    showAlert('تم تحديث بيانات المدير بنجاح!', 'success');

    // Clear form
    e.target.reset();
}

function saveSecuritySettings() {
    const settings = {
        twoFactorAuth: document.getElementById('twoFactorAuth').checked,
        loginNotifications: document.getElementById('loginNotifications').checked,
        sessionTimeout: document.getElementById('sessionTimeout').checked
    };

    localStorage.setItem('securitySettings', JSON.stringify(settings));
    showAlert('تم حفظ إعدادات الأمان!', 'success');
}

// Backup Functions
function createBackup() {
    const backupData = {
        timestamp: new Date().toISOString(),
        siteSettings: JSON.parse(localStorage.getItem('siteSettings') || '{}'),
        customTheme: JSON.parse(localStorage.getItem('customTheme') || '{}'),
        mediaFiles: JSON.parse(localStorage.getItem('mediaFiles') || '[]'),
        siteUsers: JSON.parse(localStorage.getItem('siteUsers') || '[]'),
        content: {
            hero: JSON.parse(localStorage.getItem('content_hero') || '{}'),
            courses: JSON.parse(localStorage.getItem('content_courses') || '{}'),
            about: JSON.parse(localStorage.getItem('content_about') || '{}')
        }
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showAlert('تم إنشاء النسخة الاحتياطية وتحميلها!', 'success');
}

function restoreBackup() {
    const fileInput = document.getElementById('backupFile');
    const file = fileInput.files[0];

    if (!file) {
        showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'danger');
        return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            const backupData = JSON.parse(e.target.result);

            // Restore data
            if (backupData.siteSettings) {
                localStorage.setItem('siteSettings', JSON.stringify(backupData.siteSettings));
            }
            if (backupData.customTheme) {
                localStorage.setItem('customTheme', JSON.stringify(backupData.customTheme));
            }
            if (backupData.mediaFiles) {
                localStorage.setItem('mediaFiles', JSON.stringify(backupData.mediaFiles));
            }
            if (backupData.siteUsers) {
                localStorage.setItem('siteUsers', JSON.stringify(backupData.siteUsers));
            }
            if (backupData.content) {
                Object.keys(backupData.content).forEach(key => {
                    localStorage.setItem(`content_${key}`, JSON.stringify(backupData.content[key]));
                });
            }

            showAlert('تم استعادة النسخة الاحتياطية بنجاح! سيتم إعادة تحميل الصفحة.', 'success');

            setTimeout(() => {
                location.reload();
            }, 2000);

        } catch (error) {
            showAlert('خطأ في قراءة ملف النسخة الاحتياطية', 'danger');
        }
    };
    reader.readAsText(file);
}

// Load custom theme on page load
const customTheme = JSON.parse(localStorage.getItem('customTheme'));
if (customTheme) {
    applyThemeToPage(customTheme);

    // Update form values
    if (document.getElementById('primaryColor')) {
        document.getElementById('primaryColor').value = customTheme.primaryColor;
        document.getElementById('secondaryColor').value = customTheme.secondaryColor;
        document.getElementById('successColor').value = customTheme.successColor;
        document.getElementById('warningColor').value = customTheme.warningColor;
        document.getElementById('siteFont').value = customTheme.siteFont;

        updateThemePreview();
    }
}

// Image Management Functions
function showImageManager(type) {
    // Hide all image manager sections
    document.querySelectorAll('.image-manager-section').forEach(section => {
        section.style.display = 'none';
    });

    // Show selected section
    const sectionId = type + 'ImagesManager';
    const section = document.getElementById(sectionId);
    if (section) {
        section.style.display = 'block';
    }

    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function changeImage(imageId) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                showAlert('حجم الملف كبير جداً. الحد الأقصى 5MB', 'danger');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const newImageUrl = e.target.result;

                // Update image in admin panel
                const imageSlot = document.querySelector(`[data-image-id="${imageId}"] img`);
                if (imageSlot) {
                    imageSlot.src = newImageUrl;
                }

                // Save to localStorage
                const imageData = JSON.parse(localStorage.getItem('siteImages')) || {};
                imageData[imageId] = newImageUrl;
                localStorage.setItem('siteImages', JSON.stringify(imageData));

                // Update main site
                updateMainSiteImage(imageId, newImageUrl);

                showAlert('تم تحديث الصورة بنجاح!', 'success');
            };
            reader.readAsDataURL(file);
        }
    };
    input.click();
}

function updateMainSiteImage(imageId, newImageUrl) {
    // This function would update the main site images
    // For demo purposes, we'll store in localStorage
    const mainSiteImages = JSON.parse(localStorage.getItem('mainSiteImages')) || {};
    mainSiteImages[imageId] = newImageUrl;
    localStorage.setItem('mainSiteImages', JSON.stringify(mainSiteImages));
}

function loadSiteImages() {
    const imageData = JSON.parse(localStorage.getItem('siteImages')) || {};

    Object.keys(imageData).forEach(imageId => {
        const imageSlot = document.querySelector(`[data-image-id="${imageId}"] img`);
        if (imageSlot) {
            imageSlot.src = imageData[imageId];
        }
    });
}

// Course Images Management
function manageCourseImages() {
    const coursesImagesManager = document.getElementById('coursesImagesManager');
    if (!coursesImagesManager) {
        // Create courses images manager dynamically
        const container = document.createElement('div');
        container.id = 'coursesImagesManager';
        container.className = 'image-manager-section';
        container.style.display = 'none';

        container.innerHTML = `
            <h6 class="mt-4 mb-3">صور الدورات</h6>
            <div class="row" id="courseImagesGrid">
                <!-- Course images will be loaded here -->
            </div>
        `;

        document.getElementById('mediaGrid').parentNode.insertBefore(container, document.getElementById('mediaGrid'));
        loadCourseImages();
    }
}

function loadCourseImages() {
    const courseImagesGrid = document.getElementById('courseImagesGrid');
    if (!courseImagesGrid) return;

    // Get courses data
    const courses = coursesData || [];

    courseImagesGrid.innerHTML = courses.map((course, index) => `
        <div class="col-md-4 mb-3">
            <div class="image-slot" data-image-id="courseImage${course.id}">
                <img src="${course.image}" alt="${course.title}" class="img-fluid rounded">
                <div class="image-overlay">
                    <button class="btn btn-warning btn-sm" onclick="changeCourseImage(${course.id})">
                        <i class="fas fa-edit"></i> تغيير
                    </button>
                </div>
                <p class="text-center mt-2 small">${course.title}</p>
            </div>
        </div>
    `).join('');
}

function changeCourseImage(courseId) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const newImageUrl = e.target.result;

                // Update course image in data
                const courseIndex = coursesData.findIndex(course => course.id === courseId);
                if (courseIndex !== -1) {
                    coursesData[courseIndex].image = newImageUrl;

                    // Update display
                    const imageSlot = document.querySelector(`[data-image-id="courseImage${courseId}"] img`);
                    if (imageSlot) {
                        imageSlot.src = newImageUrl;
                    }

                    // Save to localStorage
                    localStorage.setItem('coursesData', JSON.stringify(coursesData));

                    showAlert('تم تحديث صورة الدورة بنجاح!', 'success');
                }
            };
            reader.readAsDataURL(file);
        }
    };
    input.click();
}

// Initialize additional features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeMediaManager();
        loadUsers();
        initializeSecurity();
        loadSiteImages();
        manageCourseImages();

        // Show site images by default
        showImageManager('site');
    }, 100);
});
