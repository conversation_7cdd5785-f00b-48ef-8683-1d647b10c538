// Date utility functions for use across the application

// Format date to DD-MM-YYYY
function formatDateDMY(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
}

// Parse date from DD-MM-YYYY format to Date object
function parseDateDMY(dateString) {
    if (!dateString) return null;
    const [day, month, year] = dateString.split('-').map(num => parseInt(num, 10));
    return new Date(year, month - 1, day);
}

// Format date from any format to DD-MM-YYYY
function convertToGregorianDMY(dateInput) {
    if (!dateInput) return '';
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return dateInput; // Return as is if invalid
    return formatDateDMY(date);
}