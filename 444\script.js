// Global variable to store original main content
let originalMainContent = null;
let isPreviewActive = false;

// Define the complex's WhatsApp number (replace with actual number)
const COMPLEX_WHATSAPP_NUMBER = "+9647726000360"; // IMPORTANT: Use international format without spaces/symbols

// Section configuration for summary generation and WhatsApp message
const sectionsConfig = [
    { title: 'معلومات الزبون', fields: [
        { id: 'cust-booking-date', label: 'تاريخ الحجز' },
        { id: 'cust-full-name', label: 'الاسم الثلاثي واللقب' },
        { id: 'cust-mother-name', label: 'اسم الأم الثلاثي' },
        { id: 'cust-phone1', label: 'رقم الهاتف الأول' },
        { id: 'cust-phone2', label: 'رقم الهاتف الثاني' }, // Removed (اختياري) for cleaner text
        { id: 'cust-id', label: 'رقم الهوية' },
        { id: 'cust-residence-card', label: 'رقم بطاقة السكن' },
        { id: 'cust-address', label: 'العنوان الكامل', type: 'textarea' },
        { id: 'cust-source', label: 'مصدر الزبون' }
    ]},
    { title: 'معلومات الوحدة السكنية', fields: [
        { id: 'unit-area-name', label: 'اسم المنطقة' },
        { id: 'unit-number', label: 'رقم الوحدة السكنية' },
        { id: 'unit-building-number', label: 'رقم المبنى' },
        { id: 'unit-total-area', label: 'المساحة الاجمالية' }, // Simplified label
        { id: 'unit-actual-area', label: 'المساحة الفعلية' }, // Simplified label
        { id: 'unit-info-type', label: 'نوع الوحدة', type: 'select' }, // Simplified label
        { id: 'unit-classification', label: 'تصنيف الوحدة' } // Simplified label
    ]},
    { title: 'معلومات الدفع', fields: [
        { id: 'unit-price', label: 'سعر الوحدة', type: 'currency' }, // Simplified label
        { id: 'initial-payment', label: 'الدفعة الأولى', type: 'currency' },
        { id: 'payment-method', label: 'طريقة التسديد', type: 'select' },
        { id: 'exception', label: 'استثناء', type: 'select' }
    ]},
    { title: 'ملاحظات إضافية', fields: [
        { id: 'custom-notes', label: 'ملاحظات', type: 'textarea' }
    ]}
];

// Set current date for all booking date fields
function setCurrentDates() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('ar-IQ', { year: 'numeric', month: '2-digit', day: '2-digit' });
    const bookingDateInput = document.getElementById('cust-booking-date');
    if (bookingDateInput) {
        bookingDateInput.value = dateStr;
    }
}

function printPage() {
    if (!isPreviewActive) {
        generateSummary();
    }
    window.print();
}

function clearForms() {
    if (isPreviewActive) {
        restoreFormView();
    }
    const forms = document.querySelectorAll('main form');
    forms.forEach(form => form.reset());
    setCurrentDates();
    const summarySection = document.querySelector('.summary-section');
    if (summarySection) {
        summarySection.hidden = true;
    }
    // Ensure WhatsApp button is hidden when clearing/going back
    document.getElementById('whatsapp-button').hidden = true;
    document.getElementById('whatsapp-button').classList.remove('active');
}

function generateSummary() {
    const mainElement = document.querySelector('main');
    const summarySection = document.querySelector('.summary-section');
    const summaryContent = document.getElementById('summary-content');

    if (!originalMainContent) {
        originalMainContent = mainElement.innerHTML;
    }

    summaryContent.innerHTML = ''; // Clear previous summary

    // --- Add Header for Print/Summary ---
    const printHeaderContainer = document.createElement('div');
    printHeaderContainer.className = 'print-header';

    // Clone and add Left Logo
    const logoLeftSVG = document.getElementById('logo-left-svg');
    if (logoLeftSVG) {
        const logoLeftClone = logoLeftSVG.cloneNode(true);
        const logoLeftDiv = document.createElement('div');
        logoLeftDiv.className = 'print-header-logo logo-left';
        logoLeftDiv.appendChild(logoLeftClone);
        printHeaderContainer.appendChild(logoLeftDiv);
    }

    // Add Title
    const printHeaderTitle = document.createElement('div');
    printHeaderTitle.className = 'print-header-title';
    printHeaderTitle.textContent = "استمارة حجز وحدة سكنية - مجمع المؤمل السكني";
    printHeaderContainer.appendChild(printHeaderTitle);

    // Clone and add Right Logo
    const logoRightSVG = document.getElementById('logo-right-svg');
    if (logoRightSVG) {
        const logoRightClone = logoRightSVG.cloneNode(true);
        const logoRightDiv = document.createElement('div');
        logoRightDiv.className = 'print-header-logo logo-right';
        logoRightDiv.appendChild(logoRightClone);
        printHeaderContainer.appendChild(logoRightDiv);
    }

    summaryContent.appendChild(printHeaderContainer);
    // --- End Add Header ---

    const dataColumnsContainer = document.createElement('div');
    dataColumnsContainer.className = 'summary-data-columns';
    summaryContent.appendChild(dataColumnsContainer);

    sectionsConfig.forEach(sectionInfo => {
        const sectionDiv = document.createElement('div');
        sectionDiv.className = 'summary-section-group';
        sectionDiv.innerHTML = `<h3>${sectionInfo.title}</h3>`;

        sectionInfo.fields.forEach(fieldInfo => {
            const fieldElement = document.getElementById(fieldInfo.id);
            if (fieldElement) {
                let value = fieldElement.value ? fieldElement.value.trim() : '';
                if (fieldInfo.type === 'select' && fieldElement.selectedIndex >= 0 && fieldElement.value) {
                    value = fieldElement.options[fieldElement.selectedIndex].text;
                } else if (fieldInfo.type === 'currency' && value) {
                    // Use non-breaking space for currency formatting if needed, check display
                    value = `${parseFloat(value).toLocaleString('ar-IQ')} دينار عراقي`;
                }

                value = value || '<i>لم يتم إدخاله</i>';

                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'summary-field';
                if (fieldInfo.type === 'textarea') {
                    fieldDiv.classList.add('notes');
                }
                fieldDiv.innerHTML = `
                    <label>${fieldInfo.label}:</label>
                    <span>${value}</span>
                `;
                sectionDiv.appendChild(fieldDiv);
            }
        });
        dataColumnsContainer.appendChild(sectionDiv);
    });

    const importantNotesSection = document.querySelector('.important-notes-section');
    if (importantNotesSection) {
        const importantNotesClone = importantNotesSection.cloneNode(true);
        importantNotesClone.classList.remove('important-notes-section', 'b-border-box');
        importantNotesClone.classList.add('important-notes-print');
        importantNotesClone.removeAttribute('id');
        // Add header specifically for print notes section
        const notesHeader = importantNotesClone.querySelector('h3');
        if(notesHeader) notesHeader.textContent = "الملاحظات الهامة"; // Ensure consistent header
        summaryContent.appendChild(importantNotesClone);
    }

    const signatureSection = document.createElement('div');
    signatureSection.className = 'signature-section-print';
    signatureSection.innerHTML = `
        <h4>التواقيع</h4>
        <div class="signature-boxes">
            <div class="signature-box"><label>اللجنة 1:</label><span></span></div>
            <div class="signature-box"><label>اللجنة 2:</label><span></span></div>
            <div class="signature-box"><label>اللجنة 3:</label><span></span></div>
            <div class="signature-box"><label>اللجنة 4:</label><span></span></div>
        </div>
    `;
    summaryContent.appendChild(signatureSection);

    mainElement.querySelectorAll('section:not(.summary-section)').forEach(sec => sec.hidden = true);
    summarySection.hidden = false;
    isPreviewActive = true;

    // Update button visibility
    document.getElementById('preview-button').classList.remove('active');
    document.getElementById('preview-button').hidden = true;
    document.getElementById('back-to-form-button').classList.add('active');
    document.getElementById('back-to-form-button').hidden = false;
    document.getElementById('whatsapp-button').classList.add('active'); // Show WhatsApp button
    document.getElementById('whatsapp-button').hidden = false;

    summarySection.scrollIntoView({ behavior: 'smooth' });
}

function restoreFormView() {
    const mainElement = document.querySelector('main');
    const summarySection = document.querySelector('.summary-section');

    if (originalMainContent) {
        mainElement.querySelectorAll('section:not(.summary-section)').forEach(sec => sec.hidden = false);
    }
    if (summarySection) {
        summarySection.hidden = true;
    }

    isPreviewActive = false;

    // Update button visibility
    document.getElementById('preview-button').classList.add('active');
    document.getElementById('preview-button').hidden = false;
    document.getElementById('back-to-form-button').classList.remove('active');
    document.getElementById('back-to-form-button').hidden = true;
    document.getElementById('whatsapp-button').classList.remove('active'); // Hide WhatsApp button
    document.getElementById('whatsapp-button').hidden = true;

    // Scroll back to top might be useful
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function generateWhatsAppMessage() {
    let message = `*استمارة حجز وحدة سكنية - مجمع المؤمل السكني*\n------------------------------------\n\n`;

    sectionsConfig.forEach(sectionInfo => {
        message += `*${sectionInfo.title}*\n`;
        let sectionHasData = false;

        sectionInfo.fields.forEach(fieldInfo => {
            const fieldElement = document.getElementById(fieldInfo.id);
            if (fieldElement) {
                let value = fieldElement.value ? fieldElement.value.trim() : '';
                if (fieldInfo.type === 'select' && fieldElement.selectedIndex >= 0 && fieldElement.value) {
                    value = fieldElement.options[fieldElement.selectedIndex].text;
                } else if (fieldInfo.type === 'currency' && value) {
                    // Basic currency format for text message
                    value = `${parseFloat(value).toLocaleString('ar-IQ')} دينار`;
                }

                if (value && value !== '<i>لم يتم إدخاله</i>') {
                    message += `${fieldInfo.label}: ${value}\n`;
                    sectionHasData = true;
                }
            }
        });
        if (sectionHasData) {
            message += `\n`; // Add spacing between sections if data exists
        }
    });

    // Add Important Notes to WhatsApp Message
    const importantNotesList = document.querySelector('.important-notes-section ol');
    if (importantNotesList) {
        message += `*الملاحظات الهامة*\n`;
        const notesItems = importantNotesList.querySelectorAll('li');
        notesItems.forEach((item, index) => {
            message += `${index + 1}. ${item.textContent.trim()}\n`;
        });
        message += `\n`;
    }

    message += `------------------------------------`;
    return message;
}

function sendViaWhatsApp() {
    const customerPhoneInput = document.getElementById('cust-phone1');
    let customerPhone = customerPhoneInput ? customerPhoneInput.value.trim() : null;

    if (!customerPhone) {
        alert("يرجى إدخال رقم الهاتف الأول للزبون.");
        return;
    }

    // Basic cleaning: remove common symbols and spaces, ensure it starts with a plausible country code prefix if needed
    // This is a basic example; robust validation is complex. Assumes Iraqi numbers might start with 07 or +9647
    customerPhone = customerPhone.replace(/[\s+\-\(\)]/g, ''); // Remove spaces, +, -, ()
    // Optional: Add country code if missing (adjust logic as needed)
    // if (customerPhone.startsWith('07')) {
    //     customerPhone = '964' + customerPhone.substring(1);
    // } else if (!customerPhone.startsWith('9647')) {
    //      alert("صيغة رقم الهاتف غير صحيحة. يجب أن يبدأ بـ 07 أو +9647.");
    //      return;
    // }

    const message = generateWhatsAppMessage();
    const encodedMessage = encodeURIComponent(message);

    // Use the business number defined globally
    // const whatsappUrl = `https://wa.me/${COMPLEX_WHATSAPP_NUMBER}?text=${encodedMessage}`;
    // OR send TO the customer's number (more common for confirmations)
    const whatsappUrl = `https://wa.me/${customerPhone}?text=${encodedMessage}`;

    console.log("WhatsApp URL:", whatsappUrl); // For debugging
    console.log("Message:", message); // For debugging

    // Open WhatsApp link in a new tab
    window.open(whatsappUrl, '_blank');
}

document.addEventListener('DOMContentLoaded', function() {
    setCurrentDates();

    const previewButton = document.getElementById('preview-button');
    const printButton = document.getElementById('print-button');
    const clearButton = document.getElementById('clear-button');
    const backButton = document.getElementById('back-to-form-button');
    const whatsappButton = document.getElementById('whatsapp-button'); // Get WhatsApp button

    if (previewButton) previewButton.addEventListener('click', generateSummary);
    if (printButton) printButton.addEventListener('click', printPage);
    if (clearButton) clearButton.addEventListener('click', clearForms);
    if (backButton) backButton.addEventListener('click', restoreFormView);
    if (whatsappButton) whatsappButton.addEventListener('click', sendViaWhatsApp); // Add listener

    previewButton?.classList.add('active');
    printButton?.classList.add('active');
    clearButton?.classList.add('active');
    // WhatsApp button initially hidden, shown on preview
});