<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجمع المؤمل السكني</title>
    <style>
        :root {
            --primary-color: #4a6b8a;
            --secondary-color: #146444;
            --accent-color: #6b4a8a;
            --light-color: #f8f5f2;
            --dark-color: #333333;
            --border-color: #146444;
            --success-color: #5a8a4a;
            --warning-color: #146444;
            --error-color: #8a4a5a;
        }

        body {
            font-family: 'Segoe UI', Tahoma, 'Arial', sans-serif;
            line-height: 1.8;
            color: var(--dark-color);
            background-color: var(--light-color);
            margin: 0;
            padding: 0;
        }

        .main-header {
            border-bottom: 5px solid var(--secondary-color);
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            padding: 3rem;
            height: 120px;
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 2em;
            color: white;
            align-items: center;
            justify-content: space-between;
        }

        .main-header h1 {
            margin: 0;
            font-size: 2rem;
        }

        .logo svg {
            fill: white;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            height: 80px;
            width: 80px;
            transform: scale(1.5);
        }

        .logo-left circle {
            stroke: 4px;
            stroke: white;
        }

        .logo-right rect {
            fill: var(--secondary-color);
        }

        main {
            padding: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        section {
            background: white;
            border: 4px solid var(--border-color);
            box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            transition: all 0.25s ease;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
        }

        section::after {
            content: '';
            position: absolute;
            bottom: 0;
            height: 4px;
            width: 100%;
            background: linear-gradient();
        }

        section :is(h2, ol) {
            padding: 1rem;
            border-radius: 8px;
        }

        h2 {
            color: var(--primary-color);
            margin-top: 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: inherit;
            transition: all 0.3s ease;
            box-sizing: border-box; /* Ensure padding is included in width */
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        select#exception {
            /* width: 8px; Remove fixed width */
            background: #f8f8f8;
            border: 1px solid var(--border-color);
            padding: 0.75rem;
            width:100%;
            transition: all  0.3s ease;
        }

        select#exception:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .submit-button {
            display: none;
        }

        .submit-button.active {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            margin: 0.5rem;
            vertical-align: middle;
        }

        .submit-button.active:hover {
            background: var(--secondary-color);
        }

        #whatsapp-button {
            background-color: #25D366;
        }

        #whatsapp-button:hover {
            background-color: #1EAE54;
        }

        .form-group:last-child {
            margin-bottom: 0rem;
        }

        section:last-child {
            margin-bottom: 0 0 1rem;
        }

        .currency-input {
            position: relative;
            display: flex;
            align-items: center;
        }

        .currency-symbol {
            position: absolute;
            left: 1rem;
            color: #7f8c8d;
        }

        .radio-group {
            display: flex;
            gap: 1rem;
            background-color: #f8f8f8;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            margin-top: 0.5rem;
            align-items: center;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: inherit;
            font-weight: normal;
            cursor: pointer;
            padding: 0.5rem;
            transition: all 0.2s ease;
            border-radius: 4px;
        }

        .radio-group label:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .radio-group input[type="radio"]:checked + label {
            color: var(--primary-color);
            font-weight: bold;
            background-color: rgba(74, 107, 138, 0.1);
        }

        .notes-content {
            counter-reset: custom-counter;
            padding: 0;
        }

        .notes-content ol {
            padding: 0;
            margin: 1rem 0;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .notes-content li {
            position: relative;
            padding: 1rem;
            padding-right: 2.5rem;
            margin-bottom: 0;
            counter-increment: custom-counter;
            list-style-type: none;
            line-height: 1.8;
            border-bottom: 2px solid var(--light-color);
        }

        .notes-content li::before {
            content: counter(custom-counter) ".";
            position: absolute;
            right: 1rem;
            color: var(--secondary-color);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .notes-content li:not(:last-child) {
            border-bottom: 1px solid #eee;
        }

        section :is(ol, h2) {
            background: #fff;
            margin: 0;
            padding: 0;
            border-collapse: separate;
            border-spacing: 8px;
        }

        .print-controls {
            text-align: center;
            margin: 2rem auto;
            padding: 1rem;
            max-width:1200px;
        }

        .print-controls button {
            width: 100%;
            max-width: 200px;
        }

        @media (max-width: 768px) {
            .main-header {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 1em;
                padding: 1rem;
                height: auto;
            }

            .logo-left, .logo-right {
                margin: 0 auto;
            }

            .logo svg {
                height: 60px;
                width: 60px;
                transform: scale(1);
            }

            .main-header h1 {
                font-size: 1.5rem;
                margin: 0.5rem 0;
            }

            section {
                padding: 1rem;
                margin:  1rem 0;
            }

            .form-group {
                margin: 1rem;
            }

            .currency-input {
                flex-direction: column;
                align-items: flex-start; /* Align symbol to left on mobile */
            }

            .currency-symbol {
                position: relative;
                left: 0; /* Reset position */
                margin-top: 0.5rem; /* Add space above symbol */
                margin-left: 0;
            }

            .print-controls button {
                width: calc(50% - 1rem);
                margin: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .main-header h1 {
                font-size: 1rem;
            }

            .radio-group {
                flex-direction: column;
            }

            .radio-group label {
                margin: 0.5rem;
            }

            .print-controls button {
                width: calc(100% - 1rem);
            }
        }

        /* Ensure forms stack vertically on mobile */
        .vertical-form {
           display: grid;
           grid-template-columns: 1fr; /* Default to single column */
        }

        /* Use grid for larger screens */
        @media (min-width: 768px) {
            .vertical-form {
                grid-template-columns: repeat(2, 1fr); /* Two columns */
                gap: 0 1.5rem; /* Gap between columns only */
            }
            .vertical-form .full-width-group {
                grid-column: 1 / -1; /* Span full width */
            }
        }


        /* Make tables responsive */
        table {
            width: 100%;
            overflow: auto;
        }

        table th, table td {
            width: auto;
            margin: 0;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-collapse: collapse;
        }

        .notes-section {
            box-sizing: border-box;
            margin-bottom: 1.5rem;
        }

        .notes-section h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(8, 92, 145, 0.25);
        }

        .notes-section:first-of-type {
            margin-top: 2rem;
        }

        /* Add styles for the summary section */
        .summary-section {
             padding: 2rem;
        }
        .summary-section h2 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }
        .summary-content {
            padding: 1rem 0;
        }

        /* PRINT SPECIFIC STYLES */
        @media print {
            :root {
                --print-primary-color: #2c3e50;
                --print-secondary-color: #7f8c8d;
                --print-light-bg: #f8f9fa;
                --print-border-color: #dee2e6;
            }

            body {
                background-color: #fff;
                color: #000;
                font-size: 9pt;
                line-height: 1.3;
                font-family: 'Arial', sans-serif;
            }

            .container {
                width: 100%;
                min-height: auto;
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .main-header,
            .print-controls,
            .submit-button,
            #back-to-form-button,
            #preview-button,
            #whatsapp-button,
            #clear-button,
            main > section:not(.summary-section),
            .summary-section h2, /* Hide original summary h2, use print header */
            .customer-info-section .submit-button, /* Hide individual form buttons */
            .unit-info-section .submit-button,
            .payment-info-section .submit-button {
                display: none !important;
            }

            main {
                padding: 0;
                margin: 0;
            }

            .summary-section {
                display: block !important;
                border: none !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
                page-break-before: auto;
                width: 100%;
            }

            #summary-content {
                display: block !important;
                padding: 0;
                box-sizing: border-box;
                width: 100%;
            }

            .print-header {
                display: flex !important;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 0.4cm;
                margin-bottom: 0.5cm;
                border-bottom: 1.5px solid var(--print-primary-color);
                width: 100%;
                box-sizing: border-box;
            }

            .print-header-logo {
                display: block !important;
                flex-shrink: 0;
                width: 35px;
                height: 35px;
            }
            .print-header-logo svg {
                width: 100%;
                height: 100%;
                display: block;
                fill: #333;
            }
            .print-header-logo.logo-left svg circle { stroke: #333; stroke-width: 10; fill: none; }
            .print-header-logo.logo-left svg text { fill: #000; font-size: 40px; font-family: sans-serif; dominant-baseline: middle; text-anchor: middle; }
            .print-header-logo.logo-right svg rect { fill: #ccc; }
            .print-header-logo.logo-right svg text { fill: #000; font-size: 40px; font-family: sans-serif; dominant-baseline: middle; text-anchor: middle; }


            .print-header-title {
                display: block !important;
                font-size: 13pt;
                font-weight: bold;
                color: var(--print-primary-color);
                text-align: center;
                margin: 0;
                padding: 0;
                border: none;
                flex-grow: 1;
            }

            .summary-data-columns {
                column-count: 2;
                column-gap: 1cm;
                column-fill: auto;
                margin-bottom: 0.5cm;
            }

            .summary-section-group {
                break-inside: avoid-column;
                margin-bottom: 0.5cm;
                padding: 0.3cm;
                border: 1px solid var(--print-border-color);
                border-radius: 3px;
                background-color: var(--print-light-bg);
            }

            .summary-section-group h3 {
                font-size: 9.5pt;
                color: var(--print-primary-color);
                border-bottom: 1px solid var(--print-secondary-color);
                padding-bottom: 0.1cm;
                margin-top: 0;
                margin-bottom: 0.25cm;
                font-weight: bold;
            }

            .summary-field {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 0.08cm 0;
                margin-bottom: 0.05cm;
                border-bottom: 1px dotted #ccc;
                font-size: 8pt;
            }
            .summary-field:last-child { border-bottom: none; }

            .summary-field label {
                font-weight: bold;
                color: #333;
                margin-left: 0.2cm;
                flex-shrink: 0;
                width: 80px; /* Adjust as needed */
                text-align: right;
                display: inline-block; /* Make sure label takes space */
            }
            .summary-field span {
                text-align: right;
                word-break: break-word;
                color: #555;
                flex-grow: 1;
            }
            .summary-field span i { color: #999; font-style: italic; }

             .summary-field.notes {
                flex-direction: column;
                align-items: flex-start;
                border-bottom: none;
                margin-top: 0.2cm;
                break-inside: avoid; /* Prevent notes from splitting */
            }
            .summary-field.notes label {
                margin-bottom: 0.15cm;
                width: 100%; /* Make label full width */
                border-bottom: 1px solid var(--print-border-color);
                padding-bottom: 0.1cm;
                color: var(--print-primary-color);
                font-size: 8.5pt;
                text-align: right;
                display: block;
                box-sizing: border-box;
            }
            .summary-field.notes span {
                text-align: right;
                white-space: pre-wrap; /* Respect line breaks in notes */
                border: 1px solid var(--print-border-color);
                background-color: #fff;
                padding: 0.15cm;
                width: 100%;
                box-sizing: border-box;
                min-height: 30px; /* Ensure some space for notes */
                line-height: 1.3;
                font-size: 8pt;
                color: #333;
                display: block;
            }


            .important-notes-print {
                display: block !important;
                margin-top: 0.4cm;
                padding-top: 0.3cm;
                border-top: 1px solid var(--print-secondary-color);
                column-span: all; /* Span across columns */
                break-before: auto;
                page-break-inside: avoid; /* Try to keep section together */
            }
            .important-notes-print h3 {
                font-size: 10pt;
                color: var(--print-primary-color);
                margin-bottom: 0.25cm;
                font-weight: bold;
                text-align: center;
            }
             .important-notes-print ol {
                padding: 0; /* Reset padding */
                padding-right: 1.5em; /* Add padding for numbers */
                margin: 0;
                list-style-type: decimal;
                list-style-position: outside; /* Standard numbering position */
                font-size: 8pt;
                line-height: 1.3;
                color: #333;
            }
            .important-notes-print li {
                 padding: 0; /* Reset padding */
                margin-bottom: 0.15cm;
                break-inside: avoid; /* Prevent list items from splitting */
                text-align: justify;
                list-style-position: outside; /* Ensure consistent positioning */
            }
            .important-notes-print li::before {
                content: none !important; /* Remove custom counter for print */
            }

            .signature-section-print {
                display: block !important;
                margin-top: 0.7cm;
                padding-top: 0.3cm;
                border-top: 1px solid var(--print-secondary-color);
                column-span: all; /* Span across columns */
                break-before: auto; /* Allow break before if needed */
                page-break-inside: avoid; /* Try to keep signatures together */
            }
            .signature-section-print h4 {
                font-size: 10pt;
                color: var(--print-primary-color);
                margin-bottom: 0.4cm;
                font-weight: bold;
                text-align: center;
            }
            .signature-boxes {
                display: flex;
                justify-content: space-around; /* Evenly space boxes */
                align-items: flex-end; /* Align bottoms */
                flex-wrap: wrap; /* Allow wrapping if space is tight */
                gap: 0.5cm; /* Space between boxes */
            }
            .signature-box {
                width: calc(48% - 0.5cm); /* Approx two per row with gap */
                min-width: 100px; /* Minimum width */
                text-align: center;
                margin-bottom: 0.5cm; /* Space below each box */
            }
            .signature-box label {
                display: block;
                font-size: 8.5pt;
                font-weight: bold;
                margin-bottom: 0.8cm; /* Space for signature */
                color: #333;
            }
            .signature-box span { /* Represents the signature line */
                display: block;
                height: 1px;
                background-color: #888; /* Line color */
                width: 75%; /* Line width */
                margin: 0 auto; /* Center the line */
            }

            @page {
                size: A4;
                margin: 1.2cm; /* Adjust margins as needed */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="logo logo-left">
                <img src="logo+ copy.jpg" style="width: 150px; height: 150px; border-radius: 40px; border: 4px solid #146444;">
            </div>
            <h1>مجمع المؤمل السكني</h1>
            <div class="logo logo-right">
                <img src="images.png" style="width: 100px; height: 100px; border-radius: 20px; border: 4px solid #146444; margin-right: 300px;">
            </div>
        </header>

        <main>

            <section class="customer-info-section bordered bordered-or">
                <h2>معلومات الزبون</h2>
                <form id="customer-info-form" class="vertical-form">
                    <div class="form-group">
                        <label for="cust-booking-date">تاريخ الحجز:</label>
                        <input type="text" id="cust-booking-date" name="cust-booking-date" readonly>
                    </div>
                    <div class="form-group">
                        <label for="cust-full-name">الاسم الثلاثي واللقب:</label>
                        <input type="text" id="cust-full-name" name="cust-full-name" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-mother-name">اسم الأم الثلاثي:</label>
                        <input type="text" id="cust-mother-name" name="cust-mother-name" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-phone1">رقم الهاتف الأول:</label>
                        <input type="tel" id="cust-phone1" name="cust-phone1" required>
                    </div>
                    <div class="form-group">
                        <label for="cust-phone2">رقم الهاتف الثاني (اختياري):</label>
                        <input type="tel" id="cust-phone2" name="cust-phone2">
                    </div>
                    <div class="form-group">
                        <label for="cust-id">رقم الهوية:</label>
                        <input type="text" id="cust-id" name="cust-id" required>
                    </div>
                     <div class="form-group">
                        <label for="cust-residence-card">رقم بطاقة السكن:</label>
                        <input type="text" id="cust-residence-card" name="cust-residence-card" required>
                    </div>
                     <div class="form-group full-width-group">
                        <label for="cust-address">العنوان الكامل:</label>
                        <textarea id="cust-address" name="cust-address" rows="3" required></textarea>
                    </div>
                     <div class="form-group">
                        <label for="cust-source">مصدر الزبون:</label>
                        <input type="text" id="cust-source" name="cust-source">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الزبون</button>
                </form>
            </section>

            <section class="unit-info-section">
                 <h2>معلومات الوحدة السكنية</h2>
                 <form id="unit-info-form" class="vertical-form">
                     <div class="form-group">
                        <label for="unit-area-name">اسم المنطقة:</label>
                        <input type="text" id="unit-area-name" name="unit-area-name">
                    </div>
                    <div class="form-group">
                        <label for="unit-number">رقم الوحدة السكنية:</label>
                        <input type="text" id="unit-number" name="unit-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-building-number">رقم المبنى:</label>
                        <input type="text" id="unit-building-number" name="unit-building-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-total-area">المساحة الاجمالية للوحدة:</label>
                        <input type="text" id="unit-total-area" name="unit-total-area">
                    </div>
                     <div class="form-group">
                        <label for="unit-actual-area">المساحة الفعلية للوحدة:</label>
                        <input type="text" id="unit-actual-area" name="unit-actual-area">
                    </div>
                    <div class="form-group">
                        <label for="unit-info-type">نوع الوحدة السكنية:</label>
                         <select id="unit-info-type" name="unit-info-type">
                            <option value="" selected>-- اختر نوع الوحدة --</option>
                            <option value="studio">استوديو</option>
                            <option value="1bedroom">غرفة نوم واحدة</option>
                            <option value="2bedroom">غرفتين نوم</option>
                            <option value="3bedroom">ثلاث غرف نوم</option>
                            <option value="villa">فيلا</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label for="unit-classification">تصنيف الوحدة السكنية:</label>
                        <input type="text" id="unit-classification" name="unit-classification">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الوحدة</button>
                 </form>
            </section>

            <section class="payment-info-section border-box bseondar">
                <h2>معلومات الدفع</h2>
                <form id="payment-info-form" class="vertical-form">
                    <div class="form-group">
                        <label for="unit-price">سٌعر الوحدة السكنية:</label>
                        <div class="currency-input">
                            <input type="number" id="unit-price" name="unit-price" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="initial-payment">الدفعة الاولى الواجب دفعها</label>
                        <div class="currency-input">
                            <input type="number" id="initial-payment" name="initial-payment" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="payment-method">طريقة التسديد:</label>
                        <select id="payment-method" name="payment-method">
                            <option value="" selected>-- اختر طريقة التسديد --</option>
                            <option value="cash">نقداً</option>
                            <option value="installments">بالتقسيط</option>
                            <option value="bank-transfer">حوالة بنكية</option>
                            <option value="check">صك</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="exception">استثناء:</label>
                        <select id="exception" name="exception">
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الدفع</button>
                </form>
            </section>

             <section class="notes-section b-border-box">
                <h2>ملاحظات إضافية</h2>
                <div class="notes-content">
                   <textarea id="custom-notes" name="custom-notes" rows="5" placeholder="اكتب ملاحظات إضافية هنا..."></textarea>
               </div>
            </section>

            <section class="important-notes-section b-border-box">
                <h3>الملاحظات الهامة</h3>
                <div class="notes-content">
                    <ol>
                        <li>لا تعتبر هذه الاستمارة بمثابة عقد ولا جزء من عقد البيع ولا يترتب أي أي التزامات على الشركه.</li>
                        <li>الحجز ساري فقط لمدة يومان فقط بعد تاريخ الحجز اعلاه و و بعدها سيتم الغاء الحجز تلقائيا في حالة لم لم يتم تسديد الدفعة الاولى لشراء الوحدة الس  السكنية.</li>
                        <li>يحظر على المشتري التنازل عن استمارة حجز الوحدة السكنية للغير.</li>
                        <li>في حالة التعاقد بالنيابة، يؤكد الوكيل على صحة البيانات المقدمة عنه وعن المشتري، و على صحة وكالته، و أن يكون مسؤولا عما وارد من بيانات.</li>
                        <li>يكفل المشتري أنه راجع عقد البيع و فهم شروط العقد و هو مستعد للتوقيع بعد الدفعة الأولى. و يقر بتقديم الضمانات المتفق عليها للقسط المتبقي.</li>
                        <li>يقر المشتري بأن لجنة الحجز تبلغ (1.000.000 دينار عراقي) رسوم إدارية لتنظيم الحجز و تأكيد الحجز، و لا تعود حتى في حالة إلغاء العقد. و هي منفصلة عن سعر الوحدة.</li>
                        <li>في حالة سداد المبلغ في مصرف معتمد من قبل الشركة، يجب إحضار إثبات الدفع خلال فترة صلاحية الحجز. و خلاف ذلك، قد تحجز الشركة الوحدة و تقدم بديلا دون اعتراض، بحد أقصى 15 يوما من التاريخ. و بعد ذلك، يبطل الحق و يُسترد فقط المبلغ المدفوع.</li>
                        <li>في حالة انسحاب المشتري، سيتم استرداد المبلغ خلال شهرين.</li>
                        <li>أنا أقر بأنني قرأت و فهمت هذه الشروط. و أتفق على أن رسوم الحجز غير قابلة للاسترداد. و ألتزم بدفع المبلغ خلال اليوم الأول في خلال ثلاثة أيام أو أفقدي الحق في الوحدة بدون أي انتصاف قانوني.</li>
                    </ol>
                </div>
            </section>

        </main>

        <section class="summary-section b-border-box" hidden>
            <h2>ملخص المعلومات المدخلة</h2>
            <div id="summary-content" class="summary-content">
            </div>
        </section>

        <div class="print-controls">
            <button id="preview-button" class="submit-button active">معاينة المعلومات</button>
            <button id="back-to-form-button" class="submit-button" hidden>العودة إلى النموذج</button>
            <button id="print-button" class="submit-button active">طباعة المعلومات</button>
            <button id="whatsapp-button" class="submit-button" hidden>إرسال عبر واتساب</button> 
            <button id="clear-button" class="submit-button active">محو المعلومات</button>
        </div>
        <script src="script.js"></script>
    </div>
</body>
</html>