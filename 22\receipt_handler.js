document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const bookingNumberInput = document.getElementById('bookingNumberInput');
    const findBookingBtn = document.getElementById('findBookingBtn');
    const receiptContainer = document.getElementById('receiptContainer');
    const noBookingFound = document.getElementById('noBookingFound');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    const printBtn = document.getElementById('printBtn');
    const saveAsPdfBtn = document.getElementById('saveAsPdfBtn');
    
    // Receipt Elements
    const receiptBookingNumber = document.getElementById('receipt-bookingNumber');
    const receiptDate = document.getElementById('receipt-date');
    const receiptFullName = document.getElementById('receipt-fullName');
    const receiptIdNumber = document.getElementById('receipt-idNumber');
    const receiptPhone = document.getElementById('receipt-phone');
    const receiptNotes = document.getElementById('receipt-notes');
    
    // Payment Elements
    const receiptTotalPrice = document.getElementById('receipt-totalPrice');
    const receiptPaidAmount = document.getElementById('receipt-paidAmount');
    const receiptRemainingAmount = document.getElementById('receipt-remainingAmount');
    const receiptTotalInstallments = document.getElementById('receipt-totalInstallments');
    const receiptRemainingInstallments = document.getElementById('receipt-remainingInstallments');
    const receiptPaymentMethod = document.getElementById('receipt-paymentMethod');
    
    // Check for booking ID in URL (for redirects from database page)
    const urlParams = new URLSearchParams(window.location.search);
    const bookingId = urlParams.get('id');
    
    if (bookingId) {
        bookingNumberInput.value = bookingId;
        findBooking();
    }
    
    // Find booking button click
    findBookingBtn.addEventListener('click', findBooking);
    
    // Booking number input enter key
    bookingNumberInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            findBooking();
        }
    });
    
    // Try again button click
    tryAgainBtn.addEventListener('click', function() {
        noBookingFound.classList.add('d-none');
        bookingNumberInput.value = '';
        bookingNumberInput.focus();
    });
    
    // Find booking function
    function findBooking() {
        const bookingNumber = bookingNumberInput.value.trim();
        
        if (!bookingNumber) {
            alert('الرجاء إدخال رقم الحجز');
            return;
        }
        
        const booking = findBookingByNumber(bookingNumber);
        
        if (booking) {
            // Populate receipt
            populateReceipt(booking);
            // Show receipt container
            receiptContainer.classList.remove('d-none');
            noBookingFound.classList.add('d-none');
        } else {
            // Show not found message
            receiptContainer.classList.add('d-none');
            noBookingFound.classList.remove('d-none');
        }
    }
    
    // Find booking by number
    function findBookingByNumber(bookingNumber) {
        // Get bookings from localStorage
        const bookings = JSON.parse(localStorage.getItem('bookings')) || [];
        
        // Find matching booking
        return bookings.find(booking => booking.bookingNumber === bookingNumber);
    }
    
    // Get payment data for booking
    function getPaymentData(bookingNumber) {
        // Get payment plans
        const paymentPlans = JSON.parse(localStorage.getItem('paymentPlans')) || {};
        const plan = paymentPlans[bookingNumber] || {
            totalPrice: 0,
            monthsCount: 0,
            monthlyAmount: 0,
            firstPayment: 0
        };
        
        // Get payments
        const payments = JSON.parse(localStorage.getItem('payments')) || {};
        const bookingPayments = payments[bookingNumber] || [];
        
        // Calculate total paid
        const totalPaid = calculateTotalPaid(bookingPayments);
        
        // Get total price (either from plan or booking)
        const totalPrice = plan.totalPrice || 0;
        
        // Get first payment amount
        const firstPayment = plan.firstPayment || 0;
        
        // Calculate remaining amount (subtracting first payment)
        const remainingAmount = totalPrice - firstPayment - totalPaid;
        
        // Calculate total installments (from plan)
        const totalInstallments = plan.monthsCount || 0;
        
        // Calculate remaining installments
        const remainingInstallments = Math.max(0, totalInstallments - bookingPayments.length);
        
        return {
            totalPrice,
            totalPaid,
            remainingAmount,
            totalInstallments,
            remainingInstallments,
            paymentCount: bookingPayments.length,
            firstPayment
        };
    }
    
    // Calculate total paid amount from payments array
    function calculateTotalPaid(payments) {
        return payments.reduce((total, payment) => {
            return total + parseFloat(payment.amount);
        }, 0);
    }
    
    // Format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ').format(amount);
    }
    
    // Format date to DD-MM-YYYY
    function formatDateDMY(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    }
    
    // Populate receipt with booking data
    function populateReceipt(booking) {
        // Format dates to DD-MM-YYYY
        const bookingDate = new Date(booking.bookingDate);
        const formattedBookingDate = formatDateDMY(bookingDate);
        
        // Set receipt values
        receiptBookingNumber.textContent = booking.bookingNumber;
        receiptDate.textContent = formattedBookingDate;
        receiptFullName.textContent = booking.fullName + ' ' + (booking.lastName || '');
        receiptIdNumber.textContent = booking.idNumber;
        receiptPhone.textContent = booking.phone || 'غير متوفر';
        receiptNotes.textContent = booking.notes || 'لا توجد ملاحظات';
        
        // Get payment data
        const paymentData = getPaymentData(booking.bookingNumber);
        
        // Set payment values with currency formatting
        receiptTotalPrice.textContent = formatCurrency(paymentData.totalPrice) + ' دينار';
        receiptPaidAmount.textContent = formatCurrency(paymentData.totalPaid) + ' دينار';
        receiptRemainingAmount.textContent = formatCurrency(paymentData.remainingAmount) + ' دينار';
        receiptTotalInstallments.textContent = paymentData.totalInstallments;
        receiptRemainingInstallments.textContent = paymentData.remainingInstallments;
        receiptPaymentMethod.textContent = booking.paymentMethod || 'غير محدد';
    }
    
    // Print button click
    printBtn.addEventListener('click', function() {
        // Add a class to the body to enable print-specific styling
        document.body.classList.add('printing-receipt');
        window.print();
        // Remove the class after printing
        setTimeout(() => {
            document.body.classList.remove('printing-receipt');
        }, 500);
    });

    // Save as PDF button click - uses browser's print to PDF functionality
    saveAsPdfBtn.addEventListener('click', function() {
        document.body.classList.add('printing-receipt');
        window.print();
        setTimeout(() => {
            document.body.classList.remove('printing-receipt');
        }, 500);
    });
});