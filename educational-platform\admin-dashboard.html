<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة الموقع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="admin-styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-cogs me-2"></i>لوحة التحكم</h4>
            <button class="btn btn-sm btn-outline-light d-md-none" id="closeSidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#dashboard" class="nav-link active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>الرئيسية</span>
            </a>
            <a href="#site-settings" class="nav-link" data-section="site-settings">
                <i class="fas fa-cog"></i>
                <span>إعدادات الموقع</span>
            </a>
            <a href="#content-management" class="nav-link" data-section="content-management">
                <i class="fas fa-edit"></i>
                <span>إدارة المحتوى</span>
            </a>
            <a href="#theme-customizer" class="nav-link" data-section="theme-customizer">
                <i class="fas fa-palette"></i>
                <span>تخصيص التصميم</span>
            </a>
            <a href="#media-manager" class="nav-link" data-section="media-manager">
                <i class="fas fa-images"></i>
                <span>إدارة الوسائط</span>
            </a>
            <a href="#users-management" class="nav-link" data-section="users-management">
                <i class="fas fa-users"></i>
                <span>إدارة المستخدمين</span>
            </a>
            <a href="#security" class="nav-link" data-section="security">
                <i class="fas fa-shield-alt"></i>
                <span>الأمان</span>
            </a>
            <a href="#backup" class="nav-link" data-section="backup">
                <i class="fas fa-download"></i>
                <span>النسخ الاحتياطي</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary d-md-none me-3" id="toggleSidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h5 class="mb-0" id="pageTitle">لوحة التحكم الرئيسية</h5>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <a href="index.html" class="btn btn-outline-primary btn-sm" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>
                        عرض الموقع
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            المدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Dashboard Section -->
            <div class="section active" id="dashboard">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3>1,250</h3>
                                <p>إجمالي المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="stat-info">
                                <h3>50</h3>
                                <p>الدورات المتاحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="stat-info">
                                <h3>3,480</h3>
                                <p>مواضيع المنتدى</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="stat-info">
                                <h3>890</h3>
                                <p>الشهادات الصادرة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-line me-2"></i>إحصائيات الزوار</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="visitorsChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-tasks me-2"></i>المهام السريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="quick-actions">
                                    <button class="btn btn-outline-primary w-100 mb-2" onclick="switchSection('content-management')">
                                        <i class="fas fa-plus me-2"></i>إضافة محتوى جديد
                                    </button>
                                    <button class="btn btn-outline-success w-100 mb-2" onclick="switchSection('theme-customizer')">
                                        <i class="fas fa-palette me-2"></i>تخصيص التصميم
                                    </button>
                                    <button class="btn btn-outline-warning w-100 mb-2" onclick="switchSection('media-manager')">
                                        <i class="fas fa-upload me-2"></i>رفع صور جديدة
                                    </button>
                                    <button class="btn btn-outline-info w-100" onclick="switchSection('backup')">
                                        <i class="fas fa-download me-2"></i>نسخ احتياطي
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Site Settings Section -->
            <div class="section" id="site-settings">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-cog me-2"></i>إعدادات الموقع العامة</h6>
                    </div>
                    <div class="card-body">
                        <form id="siteSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الموقع</label>
                                        <input type="text" class="form-control" id="siteName" value="منصة التعلم الإلكتروني">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">وصف الموقع</label>
                                        <input type="text" class="form-control" id="siteDescription" value="منصة تعليمية متكاملة">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="siteEmail" value="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="sitePhone" value="+966 50 123 4567">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عنوان الموقع</label>
                                <textarea class="form-control" id="siteAddress" rows="3">الرياض، المملكة العربية السعودية</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Content Management Section -->
            <div class="section" id="content-management">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6><i class="fas fa-edit me-2"></i>إدارة المحتوى</h6>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-primary" onclick="showContentEditor('hero')">
                                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="showContentEditor('courses')">
                                        <i class="fas fa-book me-2"></i>الدورات
                                    </button>
                                    <button class="btn btn-sm btn-info" onclick="showContentEditor('about')">
                                        <i class="fas fa-info-circle me-2"></i>من نحن
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="contentEditor">
                                    <p class="text-muted text-center py-4">اختر قسماً لتحرير محتواه</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Theme Customizer Section -->
            <div class="section" id="theme-customizer">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-palette me-2"></i>تخصيص الألوان والتصميم</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اللون الأساسي</label>
                                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#667eea">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اللون الثانوي</label>
                                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#764ba2">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">لون النجاح</label>
                                            <input type="color" class="form-control form-control-color" id="successColor" value="#28a745">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">لون التحذير</label>
                                            <input type="color" class="form-control form-control-color" id="warningColor" value="#ffc107">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">خط الموقع</label>
                                    <select class="form-select" id="siteFont">
                                        <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI</option>
                                        <option value="'Arial', sans-serif">Arial</option>
                                        <option value="'Helvetica', sans-serif">Helvetica</option>
                                        <option value="'Times New Roman', serif">Times New Roman</option>
                                        <option value="'Cairo', sans-serif">Cairo (عربي)</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="applyTheme()">
                                    <i class="fas fa-paint-brush me-2"></i>تطبيق التصميم
                                </button>
                                <button class="btn btn-secondary ms-2" onclick="resetTheme()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-eye me-2"></i>معاينة التصميم</h6>
                            </div>
                            <div class="card-body">
                                <div class="theme-preview" id="themePreview">
                                    <div class="preview-header">
                                        <div class="preview-nav"></div>
                                    </div>
                                    <div class="preview-content">
                                        <div class="preview-hero"></div>
                                        <div class="preview-section"></div>
                                        <div class="preview-cards">
                                            <div class="preview-card"></div>
                                            <div class="preview-card"></div>
                                            <div class="preview-card"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Manager Section -->
            <div class="section" id="media-manager">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6><i class="fas fa-images me-2"></i>مكتبة الوسائط</h6>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-primary" onclick="showImageManager('site')">
                                        <i class="fas fa-globe me-2"></i>صور الموقع
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="showImageManager('courses')">
                                        <i class="fas fa-book me-2"></i>صور الدورات
                                    </button>
                                    <button class="btn btn-sm btn-info" onclick="showImageManager('instructors')">
                                        <i class="fas fa-users me-2"></i>صور المدرسين
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>اسحب الملفات هنا أو انقر للرفع</h5>
                                    <p class="text-muted">يدعم: JPG, PNG, GIF (حد أقصى 5MB)</p>
                                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                                </div>

                                <!-- Site Images Management -->
                                <div id="siteImagesManager" class="image-manager-section">
                                    <h6 class="mt-4 mb-3">صور الموقع الرئيسية</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="heroImage1">
                                                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                                     alt="صورة البطل 1" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('heroImage1')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">صورة البطل الأولى</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="heroImage2">
                                                <img src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                                     alt="صورة البطل 2" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('heroImage2')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">صورة البطل الثانية</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="heroImage3">
                                                <img src="https://images.unsplash.com/photo-1515378791036-0648a814c963?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                                     alt="صورة البطل 3" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('heroImage3')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">صورة البطل الثالثة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Instructors Images Management -->
                                <div id="instructorsImagesManager" class="image-manager-section" style="display: none;">
                                    <h6 class="mt-4 mb-3">صور المدرسين</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="instructorImage1">
                                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                                     alt="أحمد محمد" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('instructorImage1')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">أحمد محمد</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="instructorImage2">
                                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                                     alt="فاطمة أحمد" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('instructorImage2')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">فاطمة أحمد</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="image-slot" data-image-id="instructorImage3">
                                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                                     alt="محمد علي" class="img-fluid rounded">
                                                <div class="image-overlay">
                                                    <button class="btn btn-warning btn-sm" onclick="changeImage('instructorImage3')">
                                                        <i class="fas fa-edit"></i> تغيير
                                                    </button>
                                                </div>
                                                <p class="text-center mt-2 small">محمد علي</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="media-grid mt-4" id="mediaGrid">
                                    <!-- Media items will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الملف</h6>
                            </div>
                            <div class="card-body">
                                <div id="fileInfo">
                                    <p class="text-muted text-center">اختر ملفاً لعرض معلوماته</p>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-pie me-2"></i>إحصائيات التخزين</h6>
                            </div>
                            <div class="card-body">
                                <div class="storage-info">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المستخدم</span>
                                        <span>2.3 GB / 10 GB</span>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar" style="width: 23%"></div>
                                    </div>
                                    <small class="text-muted">متبقي 7.7 GB</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management Section -->
            <div class="section" id="users-management">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-users me-2"></i>إدارة المستخدمين</h6>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-2"></i>إضافة مستخدم
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>النوع</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTable">
                                    <!-- Users will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="section" id="security">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-key me-2"></i>تغيير بيانات المدير</h6>
                            </div>
                            <div class="card-body">
                                <form id="adminCredentialsForm">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم الجديد</label>
                                        <input type="text" class="form-control" id="newUsername" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="newPassword" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">تأكيد كلمة المرور</label>
                                        <input type="password" class="form-control" id="confirmPassword" required>
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-2"></i>تحديث البيانات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</h6>
                            </div>
                            <div class="card-body">
                                <div class="security-option mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                        <label class="form-check-label" for="twoFactorAuth">
                                            تفعيل المصادقة الثنائية
                                        </label>
                                    </div>
                                </div>
                                <div class="security-option mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="loginNotifications" checked>
                                        <label class="form-check-label" for="loginNotifications">
                                            إشعارات تسجيل الدخول
                                        </label>
                                    </div>
                                </div>
                                <div class="security-option mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="sessionTimeout" checked>
                                        <label class="form-check-label" for="sessionTimeout">
                                            انتهاء الجلسة التلقائي
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-success" onclick="saveSecuritySettings()">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup Section -->
            <div class="section" id="backup">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">قم بإنشاء نسخة احتياطية من جميع بيانات الموقع</p>
                                <div class="backup-options mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="backupContent" checked>
                                        <label class="form-check-label" for="backupContent">
                                            المحتوى والنصوص
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="backupMedia" checked>
                                        <label class="form-check-label" for="backupMedia">
                                            الوسائط والصور
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="backupSettings" checked>
                                        <label class="form-check-label" for="backupSettings">
                                            الإعدادات والتخصيصات
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="createBackup()">
                                    <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-upload me-2"></i>استعادة نسخة احتياطية</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">استعادة البيانات من نسخة احتياطية سابقة</p>
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="backupFile" accept=".json">
                                </div>
                                <button class="btn btn-warning" onclick="restoreBackup()">
                                    <i class="fas fa-upload me-2"></i>استعادة النسخة
                                </button>
                                <div class="alert alert-warning mt-3">
                                    <small><i class="fas fa-exclamation-triangle me-2"></i>تحذير: ستحل البيانات المستعادة محل البيانات الحالية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="userType" required>
                                <option value="student">طالب</option>
                                <option value="teacher">مدرس</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addUser()">إضافة المستخدم</button>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="admin-script.js"></script>
</body>
</html>
