<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجمع المؤمل السكني</title>
    <style>
        :root {
            --primary-color: #4a6b8a;
            --secondary-color: #146444; 
            --accent-color: #6b4a8a;
            --light-color: #f8f5f2;
            --dark-color: #333333;
            --border-color: #146444; 
            --success-color: #5a8a4a;
            --warning-color: #8a784a;
            --error-color: #8a4a5a;
        }

        body {
            font-family: 'Segoe UI', Tahoma, 'Arial', sans-serif;
            line-height: 1.8;
            color: var(--dark-color);
            background-color: var(--light-color);
            margin: 0;
            padding: 0;
        }

        .main-header {
            border-bottom: 5px solid var(--secondary-color);
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            padding: 3rem;
            height: 120px;
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 2em;
            color: white;
            align-items: center;
            justify-content: space-between;
        }

        .main-header h1 {
            margin: 0;
            font-size: 2rem;
        }

        .logo svg {
            fill: white;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            height: 80px;
            width: 80px;
            transform: scale(1.5);
        }

        .logo-left circle {
            stroke: 4px;
            stroke: white;
        }

        .logo-right rect {
            fill: var(--secondary-color);
        }

        main {
            padding: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        section {
            background: white;
            border: 4px solid var(--border-color);
            box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            transition: all 0.25s ease;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
        }

        section::after {
            content: '';
            position: absolute;
            bottom: 0;
            height: 4px;
            width: 100%;
            background: linear-gradient();
        }

        section :is(h2, ol) {
            padding: 1rem;
            border-radius: 8px;
        }

        h2 {
            color: var(--primary-color);
            margin-top: 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: inherit;
            transition: all 0.3s ease;
            box-sizing: border-box; /* Ensure padding is included in width */
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(20, 100, 68, 0.2); 
        }

        select#exception {
            /* width: 8px; Remove fixed width */
            background: #f8f8f8;
            border: 1px solid var(--border-color);
            padding: 0.75rem;
            width:100%;
            transition: all  0.3s ease;
        }

        select#exception:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(20, 100, 68, 0.2); 
        }

        .submit-button {
            display: none;
        }

        .submit-button.active {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            margin: 0.5rem;
            vertical-align: middle;
        }

        .submit-button.active:hover {
            background: var(--secondary-color);
        }

        #whatsapp-button {
            background-color: #25D366;
        }

        #whatsapp-button:hover {
            background-color: #1EAE54;
        }

        .form-group:last-child {
            margin-bottom: 0rem;
        }

        section:last-child {
            margin-bottom: 0 0 1rem;
        }

        .currency-input {
            position: relative;
            display: flex;
            align-items: center;
        }

        .currency-symbol {
            position: absolute;
            left: 1rem;
            color: #7f8c8d;
        }

        .radio-group {
            display: flex;
            gap: 1rem;
            background-color: #f8f8f8;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            margin-top: 0.5rem;
            align-items: center;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: inherit;
            font-weight: normal;
            cursor: pointer;
            padding: 0.5rem;
            transition: all 0.2s ease;
            border-radius: 4px;
        }

        .radio-group label:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .radio-group input[type="radio"]:checked + label {
            color: var(--primary-color);
            font-weight: bold;
            background-color: rgba(74, 107, 138, 0.1);
        }

        .notes-content {
            counter-reset: custom-counter;
            padding: 0;
        }

        .notes-content ol {
            padding: 0;
            margin: 1rem 0;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .notes-content li {
            position: relative;
            padding: 1rem;
            padding-right: 2.5rem;
            margin-bottom: 0;
            counter-increment: custom-counter;
            list-style-type: none;
            line-height: 1.8;
            border-bottom: 2px solid var(--light-color);
        }

        .notes-content li::before {
            content: counter(custom-counter) ".";
            position: absolute;
            right: 1rem;
            color: var(--secondary-color);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .notes-content li:not(:last-child) {
            border-bottom: 1px solid #eee;
        }

        section :is(ol, h2) {
            background: #fff;
            margin: 0;
            padding: 0;
            border-collapse: separate;
            border-spacing: 8px;
        }

        .print-controls {
            text-align: center;
            margin: 2rem auto;
            padding: 1rem;
            max-width:1200px;
        }

        .print-controls button {
            width: 100%;
            max-width: 200px;
        }

        @media (max-width: 768px) {
            .main-header {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 1em;
                padding: 1rem;
                height: auto;
            }

            .logo-left, .logo-right {
                margin: 0 auto;
            }

            .logo svg {
                height: 60px;
                width: 60px;
                transform: scale(1);
            }

            .main-header h1 {
                font-size: 1.5rem;
                margin: 0.5rem 0;
            }

            section {
                padding: 1rem;
                margin:  1rem 0;
            }

            .form-group {
                margin: 1rem;
            }

            .currency-input {
                flex-direction: column;
                align-items: flex-start; /* Align symbol to left on mobile */
            }

            .currency-symbol {
                position: relative;
                left: 0; /* Reset position */
                margin-top: 0.5rem; /* Add space above symbol */
                margin-left: 0;
            }

            .print-controls button {
                width: calc(50% - 1rem);
                margin: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .main-header h1 {
                font-size: 1rem;
            }

            .radio-group {
                flex-direction: column;
            }

            .radio-group label {
                margin: 0.5rem;
            }

            .print-controls button {
                width: calc(100% - 1rem);
            }
        }

        /* Ensure forms stack vertically on mobile */
        .vertical-form {
           display: grid;
           grid-template-columns: 1fr; /* Default to single column */
        }

        /* Use grid for larger screens */
        @media (min-width: 768px) {
            .vertical-form {
                grid-template-columns: repeat(2, 1fr); /* Two columns */
                gap: 0 1.5rem; /* Gap between columns only */
            }
            .vertical-form .full-width-group {
                grid-column: 1 / -1; /* Span full width */
            }
        }


        /* Make tables responsive */
        table {
            width: 100%;
            overflow: auto;
        }

        table th, table td {
            width: auto;
            margin: 0;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-collapse: collapse;
        }

        .notes-section {
            box-sizing: border-box;
            margin-bottom: 1.5rem;
        }

        .notes-section h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color); 
        }

        .notes-section:first-of-type {
            margin-top: 2rem;
        }

        /* Add styles for the summary section */
        .summary-section {
             padding: 2rem;
             background-color: var(--light-color); 
             border: 1px solid var(--border-color); 
             border-radius: 8px; 
        }
        .summary-section h2 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }
        .summary-content {
            padding: 1rem 0;
        }
        /* Style the columns for better visual separation */
        .summary-data-columns {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 1.5rem;
        }

        /* Style individual summary groups */
        .summary-section-group {
            background-color: #fff; 
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); 
            break-inside: avoid; 
        }

        .summary-section-group h3 {
            font-size: 1.1rem; 
            color: var(--primary-color);
            border-bottom: 1px solid var(--secondary-color);
            padding-bottom: 0.4rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        /* Style individual fields within groups */
        .summary-field {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0.5rem 0; 
            margin-bottom: 0.5rem;
            border-bottom: 1px dashed var(--border-color); 
            font-size: 0.95rem; 
        }
        .summary-field:last-child {
            border-bottom: none;
        }

        .summary-field label {
            font-weight: 600; 
            color: var(--dark-color);
            margin-left: 0.5rem; 
            flex-basis: 120px; 
            flex-shrink: 0;
            text-align: right;
            border: 1px solid transparent; 
            padding: 0.2rem 0.4rem; 
            border-radius: 3px; 
        }

        .summary-field span {
            text-align: right;
            word-break: break-word;
            color: var(--secondary-color); 
            flex-grow: 1;
            border: 1px solid var(--border-color); 
            padding: 0.2rem 0.4rem; 
            background-color: #f9f9f9; 
            border-radius: 3px; 
            min-height: 1.5em; 
            display: inline-block; 
        }
        .summary-field span i {
             color: #999;
             font-style: italic;
        }

        /* Specific styling for notes field in summary */
        .summary-field.notes {
            flex-direction: column;
            align-items: stretch; 
        }
        .summary-field.notes label {
            width: auto; 
            flex-basis: auto;
            text-align: right; 
            margin-bottom: 0.3rem;
            border: none; 
            background: none;
            padding: 0;
        }
        .summary-field.notes span {
            width: 100%; 
            box-sizing: border-box;
            white-space: pre-wrap;
            min-height: 50px;
            text-align: right; 
            line-height: 1.5;
            border: 1px solid var(--border-color); 
        }

        /* Keep important notes and signatures outside the column layout */
        .important-notes-print,
        .signature-section-print {
             grid-column: 1 / -1; 
             margin-top: 1.5rem;
             padding-top: 1rem;
             border-top: 1px solid var(--secondary-color);
        }

        /* PRINT SPECIFIC STYLES */
        @media print {
            :root {
                --print-primary-color: #2c3e50;
                --print-secondary-color: #7f8c8d; 
                --print-light-bg: #f8f9fa;
                --print-border-color: #dee2e6; 
            }

            body {
                background-color: #fff;
                color: #000;
                font-size: 9pt;
                line-height: 1.3;
                font-family: 'Arial', sans-serif;
            }

            .container {
                width: 100%;
                min-height: auto;
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .main-header, 
            .print-controls,
            .submit-button,
            #back-to-form-button,
            #preview-button,
            #whatsapp-button,
            #clear-button,
            main > section:not(.summary-section), 
            .summary-section h2, 
            .customer-info-section .submit-button, 
            .unit-info-section .submit-button,
            .payment-info-section .submit-button {
                display: none !important;
            }

            main {
                padding: 0;
                margin: 0;
            }

            .summary-section {
                display: block !important; 
                border: none !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
                page-break-before: auto;
                width: 100%;
                background-color: transparent !important; 
            }

            #summary-content {
                display: block !important;
                padding: 0;
                box-sizing: border-box;
                width: 100%;
            }

            .print-header {
                display: flex !important; 
                justify-content: space-between; 
                align-items: center; 
                padding-bottom: 0.4cm;
                margin-bottom: 0.5cm;
                border-bottom: 1.5px solid var(--print-primary-color); 
                width: 100%;
                box-sizing: border-box;
            }

            .print-header-logo {
                display: block !important; 
                flex-shrink: 0; 
                width: 35px; 
                height: 35px; 
            }
            .print-header-logo svg { 
                width: 100%;
                height: 100%;
                display: block; 
                fill: #333; 
            }
             .print-header-logo.logo-left svg circle {
                stroke: #333; 
                stroke-width: 10; 
                fill: none; 
            }
            .print-header-logo.logo-left svg text {
                fill: #000; 
                font-size: 40px; 
                font-family: sans-serif; 
                dominant-baseline: middle;
                text-anchor: middle;
            }
             .print-header-logo.logo-right svg rect {
                fill: #cccccc; 
            }
            .print-header-logo.logo-right svg text {
                fill: #000; 
                font-size: 40px; 
                font-family: sans-serif; 
                dominant-baseline: middle;
                text-anchor: middle;
            }


            .print-header-title {
                display: block !important; 
                font-size: 13pt;
                font-weight: bold;
                color: var(--print-primary-color);
                text-align: center;
                margin: 0;
                padding: 0;
                border: none;
                flex-grow: 1; 
            }

            /* Use columns for print layout */
            .summary-data-columns {
                column-count: 2;
                column-gap: 1cm;
                column-fill: auto;
                margin-bottom: 0.5cm;
                display: block; 
                grid-template-columns: none; 
            }

            .summary-section-group {
                break-inside: avoid-column; 
                margin-bottom: 0.5cm;
                padding: 0.3cm;
                border: 1px solid var(--print-border-color); 
                border-radius: 3px;
                background-color: var(--print-light-bg);
                box-shadow: none; 
            }

            .summary-section-group h3 {
                font-size: 9.5pt;
                color: var(--print-primary-color);
                border-bottom: 1px solid var(--print-secondary-color); 
                padding-bottom: 0.1cm;
                margin-top: 0;
                margin-bottom: 0.25cm;
                font-weight: bold;
            }

            .summary-field {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 0.08cm 0;
                margin-bottom: 0.05cm;
                border-bottom: 1px dotted #ccc; 
                font-size: 8pt;
            }
            .summary-field:last-child { border-bottom: none; }

            .summary-field label {
                font-weight: bold;
                color: #333;
                margin-left: 0.2cm;
                flex-shrink: 0;
                flex-basis: 80px; 
                text-align: right;
                display: inline-block;
                border: 1px solid #ccc; 
                padding: 0.05cm 0.1cm; 
                background-color: #eee; 
                 border-radius: 2px;
            }
            .summary-field span {
                text-align: right;
                word-break: break-word;
                color: #444; 
                flex-grow: 1;
                border: 1px solid #ccc; 
                padding: 0.05cm 0.1cm; 
                background-color: #fff; 
                 border-radius: 2px;
                 min-height: 1.3em; 
                 display: inline-block;
            }
            .summary-field span i { color: #999; font-style: italic; }

            .summary-field.notes {
                flex-direction: column;
                align-items: flex-start;
                border-bottom: none;
                margin-top: 0.2cm;
                break-inside: avoid;
            }
            .summary-field.notes label {
                margin-bottom: 0.15cm;
                width: 100%;
                border-bottom: 1px solid var(--print-border-color); 
                padding-bottom: 0.1cm;
                color: var(--print-primary-color);
                font-size: 8.5pt;
                text-align: right;
                display: block;
                box-sizing: border-box;
                border: none; 
                background: none;
                padding: 0;
            }
            .summary-field.notes span {
                text-align: right;
                white-space: pre-wrap;
                border: 1px solid var(--print-border-color); 
                background-color: #fff;
                padding: 0.15cm;
                width: 100%;
                box-sizing: border-box;
                min-height: 30px;
                line-height: 1.3;
                font-size: 8pt;
                color: #333;
                display: block;
            }

            .important-notes-print {
                display: block !important; 
                margin-top: 0.4cm;
                padding-top: 0.3cm;
                border-top: 1px solid var(--print-secondary-color); 
                column-span: all; 
                break-before: auto;
                page-break-inside: avoid; 
            }
            .important-notes-print h3 {
                font-size: 10pt;
                color: var(--print-primary-color);
                margin-bottom: 0.25cm;
                font-weight: bold;
                text-align: center;
            }
             .important-notes-print ol {
                padding: 0; 
                padding-right: 1.5em; 
                margin: 0;
                list-style-type: decimal; 
                list-style-position: outside; 
                font-size: 8pt;
                line-height: 1.3;
                color: #333;
            }
            .important-notes-print li {
                 padding: 0; 
                margin-bottom: 0.15cm;
                break-inside: avoid; 
                text-align: justify;
                list-style-position: outside; 
            }
            .important-notes-print li::before {
                content: none !important; 
            }

            .signature-section-print {
                display: block !important; 
                margin-top: 0.7cm;
                padding-top: 0.3cm;
                border-top: 1px solid var(--print-secondary-color); 
                column-span: all; 
                break-before: auto; 
                page-break-inside: avoid; 
            }
            .signature-section-print h4 {
                font-size: 10pt;
                color: var(--print-primary-color);
                margin-bottom: 0.4cm;
                font-weight: bold;
                text-align: center;
            }
            .signature-boxes {
                display: flex;
                justify-content: space-around; 
                align-items: flex-end; 
                flex-wrap: wrap; 
                gap: 0.5cm; 
            }
            .signature-box {
                width: calc(48% - 0.5cm); 
                min-width: 100px; 
                text-align: center;
                margin-bottom: 0.5cm; 
            }
            .signature-box label {
                display: block;
                font-size: 8.5pt;
                font-weight: bold;
                margin-bottom: 0.8cm; 
                color: #333;
            }
            .signature-box span { 
                display: block;
                height: 1px;
                background-color: #888; 
                width: 75%; 
                margin: 0 auto; 
            }

            @page {
                size: A4;
                margin: 1.2cm; 
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="logo logo-left">

                <img src="logo+ copy.jpg" style="width: 150px; height: 150px; border-radius: 40px; border: 4px solid #146444;">


            </div>
            <h1>مجمع المؤمل السكني</h1>

            <div class="logo logo-right">

                <img src="images.png" style="width: 120px; height: 120px; border-radius: 20px; border: 4px solid #146444;">

            </div>
        </header>

        <main>
            <section class="customer-info-section bordered bordered-or">
                <h2>معلومات الزبون</h2>
                <form id="customer-info-form" class="vertical-form">
                    <div class="form-group">
                        <label for="cust-booking-date">تاريخ الحجز:</label>
                        <input type="text" id="cust-booking-date" name="cust-booking-date" readonly>
                    </div>
                    <div class="form-group">
                        <label for="cust-full-name">الاسم الثلاثي واللقب:</label>
                        <input type="text" id="cust-full-name" name="cust-full-name" >
                    </div>
                    <div class="form-group">
                        <label for="cust-mother-name">اسم الأم الثلاثي:</label>
                        <input type="text" id="cust-mother-name" name="cust-mother-name" >
                    </div>
                    <div class="form-group">
                        <label for="cust-phone1">رقم الهاتف الأول:</label>
                        <input type="tel" id="cust-phone1" name="cust-phone1" >
                    </div>
                    <div class="form-group">
                        <label for="cust-phone2">رقم الهاتف الثاني (اختياري):</label>
                        <input type="tel" id="cust-phone2" name="cust-phone2">
                    </div>
                    <div class="form-group">
                        <label for="cust-id">رقم الهوية:</label>
                        <input type="text" id="cust-id" name="cust-id" >
                    </div>
                     <div class="form-group">
                        <label for="cust-residence-card">رقم بطاقة السكن:</label>
                        <input type="text" id="cust-residence-card" name="cust-residence-card" >
                    </div>
                     <div class="form-group full-width-group">
                        <label for="cust-address">العنوان الكامل:</label>
                        <textarea id="cust-address" name="cust-address" rows="3" ></textarea>
                    </div>
                     <div class="form-group">
                        <label for="cust-source">مصدر الزبون:</label>
                        <input type="text" id="cust-source" name="cust-source">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الزبون</button>
                </form>
            </section>

            <section class="unit-info-section">
                 <h2>معلومات الوحدة السكنية</h2>
                 <form id="unit-info-form" class="vertical-form">
                     <div class="form-group">
                        <label for="unit-area-name">اسم المنطقة:</label>
                        <input type="text" id="unit-area-name" name="unit-area-name">
                    </div>
                    <div class="form-group">
                        <label for="unit-number">رقم الوحدة السكنية:</label>
                        <input type="text" id="unit-number" name="unit-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-building-number">رقم المبنى:</label>
                        <input type="text" id="unit-building-number" name="unit-building-number">
                    </div>
                    <div class="form-group">
                        <label for="unit-total-area">المساحة الاجمالية للوحدة:</label>
                        <input type="text" id="unit-total-area" name="unit-total-area">
                    </div>
                     <div class="form-group">
                        <label for="unit-actual-area">المساحة الفعلية للوحدة:</label>
                        <input type="text" id="unit-actual-area" name="unit-actual-area">
                    </div>
                    <div class="form-group">
                        <label for="unit-info-type">نوع الوحدة السكنية:</label>
                         <select id="unit-info-type" name="unit-info-type">
                            <option value="" selected>-- اختر نوع الوحدة --</option>
                            <option value="studio">استوديو</option>
                            <option value="1bedroom">غرفة نوم واحدة</option>
                            <option value="2bedroom">غرفتين نوم</option>
                            <option value="3bedroom">ثلاث غرف نوم</option>
                            <option value="villa">فيلا</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label for="unit-classification">تصنيف الوحدة السكنية:</label>
                        <input type="text" id="unit-classification" name="unit-classification">
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الوحدة</button>
                 </form>
            </section>

            <section class="payment-info-section border-box bseondar">
                <h2>معلومات الدفع</h2>
                <form id="payment-info-form" class="vertical-form">
                    <div class="form-group">
                        <label for="unit-price">سٌعر الوحدة السكنية:</label>
                        <div class="currency-input">
                            <input type="number" id="unit-price" name="unit-price" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="initial-payment">الٌالدفعة الٌأولىلى:</label>
                        <div class="currency-input">
                            <input type="number" id="initial-payment" name="initial-payment" placeholder="0">
                            <span class="currency-symbol">دينار عراقي</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="payment-method">طريقة التسديد:</label>
                        <select id="payment-method" name="payment-method">
                            <option value="" selected>-- اختر طريقة التسديد --</option>
                            <option value="cash">نقداً</option>
                            <option value="installments">بالتقسيط</option>
                            <option value="bank-transfer">حوالة بنكية</option>
                            <option value="check">صك</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="exception">استثناء:</label>
                        <select id="exception" name="exception">
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>
                    <button type="submit" class="submit-button">حفظ معلومات الدفع</button>
                </form>
            </section>

             <section class="notes-section b-border-box">
                <h2>ملاحظات إضافية</h2>
                <div class="notes-content">
                   <textarea id="custom-notes" name="custom-notes" rows="5" placeholder="اكتب ملاحظات إضافية هنا..."></textarea>
               </div>
            </section>

            <section class="important-notes-section b-border-box">
                <h3>الملاحظات الهامة</h3>
                <div class="notes-content">
                    <ol>
                        <li>لا تعتبر هذه الاستمارة بمثابة عقد ولا جزء من عقد البيع ولا يترتب أي أي التزامات على الشركه.</li>
                        <li>الحجز ساري فقط لمدة يومان فقط بعد تاريخ الحجز اعلاه و و بعدها سيتم الغاء الحجز تلقائيا في حالة لم لم يتم تسديد الدفعة الاولى لشراء الوحدة الس  السكنية.</li>
                        <li>يحظر على المشتري التنازل عن استمارة حجز الوحدة السكنية للغير.</li>
                        <li>في حالة التعاقد بالنيابة، يؤكد الوكيل على صحة البيانات المقدمة عنه وعن المشتري، و على صحة وكالته، و أن يكون مسؤولا عما وارد من بيانات.</li>
                        <li>يكفل المشتري أنه راجع عقد البيع و فهم شروط العقد و هو مستعد للتوقيع بعد الدفعة الأولى. و يقر بتقديم الضمانات المتفق عليها للقسط المتبقي.</li>
                        <li>يقر المشتري بأن لجنة الحجز تبلغ (1.000.000 دينار عراقي) رسوم إدارية لتنظيم الحجز و تأكيد الحجز، و لا تعود حتى في حالة إلغاء العقد. و هي منفصلة عن سعر الوحدة.</li>
                        <li>في حالة سداد المبلغ في مصرف معتمد من قبل الشركة، يجب إحضار إثبات الدفع خلال فترة صلاحية الحجز. و خلاف ذلك، قد تحجز الشركة الوحدة و تقدم بديلا دون اعتراض، بحد أقصى 15 يوما من التاريخ. و بعد ذلك، يبطل الحق و يُسترد فقط المبلغ المدفوع.</li>
                        <li>في حالة انسحاب المشتري، سيتم استرداد المبلغ خلال شهرين.</li>
                        <li>أنا أقر بأنني قرأت و فهمت هذه الشروط. و أتفق على أن رسوم الحجز غير قابلة للاسترداد. و ألتزم بدفع المبلغ خلال اليوم الأول في خلال ثلاثة أيام أو أفقدي الحق في الوحدة بدون أي انتصاف قانوني.</li>
                    </ol>
                </div>
            </section>

        </main>

        <section class="summary-section b-border-box" hidden>
            <div id="summary-content" class="summary-content">
            </div>
        </section>

        <div class="print-controls">
            <button id="preview-button" class="submit-button active">معاينة المعلومات</button>
            <button id="back-to-form-button" class="submit-button" hidden>العودة إلى النموذج</button>
            <button id="print-button" class="submit-button active">طباعة المعلومات</button>
            <button id="whatsapp-button" class="submit-button" hidden>إرسال عبر واتساب</button> 
            <button id="clear-button" class="submit-button active">محو المعلومات</button>
        </div>

        <script>
            let isPreviewActive = false;

            const COMPLEX_WHATSAPP_NUMBER = "+9647712345678";

            const sectionsConfig = [
                { title: 'معلومات الزبون', fields: [
                    { id: 'cust-booking-date', label: 'تاريخ الحجز' },
                    { id: 'cust-full-name', label: 'الاسم الثلاثي واللقب' },
                    { id: 'cust-mother-name', label: 'اسم الأم الثلاثي' },
                    { id: 'cust-phone1', label: 'رقم الهاتف الأول' },
                    { id: 'cust-phone2', label: 'رقم الهاتف الثاني' },
                    { id: 'cust-id', label: 'رقم الهوية' },
                    { id: 'cust-residence-card', label: 'رقم بطاقة السكن' },
                    { id: 'cust-address', label: 'العنوان الكامل', type: 'textarea' },
                    { id: 'cust-source', label: 'مصدر الزبون' }
                ]},
                { title: 'معلومات الوحدة السكنية', fields: [
                    { id: 'unit-area-name', label: 'اسم المنطقة' },
                    { id: 'unit-number', label: 'رقم الوحدة السكنية' },
                    { id: 'unit-building-number', label: 'رقم المبنى' },
                    { id: 'unit-total-area', label: 'المساحة الاجمالية' },
                    { id: 'unit-actual-area', label: 'المساحة الفعلية' },
                    { id: 'unit-info-type', label: 'نوع الوحدة', type: 'select' },
                    { id: 'unit-classification', label: 'تصنيف الوحدة' }
                ]},
                { title: 'معلومات الدفع', fields: [
                    { id: 'unit-price', label: 'سعر الوحدة', type: 'currency' },
                    { id: 'initial-payment', label: 'الدفعة الأولى', type: 'currency' },
                    { id: 'payment-method', label: 'طريقة التسديد', type: 'select' },
                    { id: 'exception', label: 'استثناء', type: 'select' }
                ]},
                { title: 'ملاحظات إضافية', fields: [
                    { id: 'custom-notes', label: 'ملاحظات', type: 'textarea' }
                ]}
            ];

            function setCurrentDates() {
                const now = new Date();
                const dateStr = now.toLocaleDateString('ar-IQ', { year: 'numeric', month: '2-digit', day: '2-digit' });
                const bookingDateInput = document.getElementById('cust-booking-date');
                if (bookingDateInput) {
                    bookingDateInput.value = dateStr;
                }
            }

            function printPage() {
                 // If not in preview mode, generate the summary first, then print
                 if (!isPreviewActive) {
                    generateSummary();
                    // Use a small timeout to allow the DOM to update before printing
                    setTimeout(() => {
                        window.print();
                    }, 150); // Slightly increased delay
                 } else {
                    // Already in preview mode, just print
                    window.print();
                 }
            }


            function clearForms() {
                if (isPreviewActive) {
                    restoreFormView(); // Go back to form view if in preview
                }
                const forms = document.querySelectorAll('main form');
                forms.forEach(form => {
                    form.reset();
                    // Clear validation styles if any
                    form.querySelectorAll('.invalid').forEach(el => el.classList.remove('invalid'));
                });
                setCurrentDates(); // Reset date
                // Ensure summary is hidden
                const summarySection = document.querySelector('.summary-section');
                if (summarySection) {
                    summarySection.hidden = true;
                    summarySection.querySelector('#summary-content').innerHTML = ''; // Clear content
                }
                // Ensure button states are reset
                 document.getElementById('preview-button').classList.add('active');
                 document.getElementById('preview-button').hidden = false;
                 document.getElementById('back-to-form-button').classList.remove('active');
                 document.getElementById('back-to-form-button').hidden = true;
                 document.getElementById('whatsapp-button').classList.remove('active');
                 document.getElementById('whatsapp-button').hidden = true;
                 isPreviewActive = false;
                 window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            // Simple validation function (can be expanded, but not currently used for required fields)
             function validateForms() {
                let isValid = true;
                // This function is no longer enforcing required fields.
                // Could be used for format validation (e.g., email, phone) later.
                const allInputs = document.querySelectorAll('main form input, main form select, main form textarea');
                allInputs.forEach(input => {
                    input.classList.remove('invalid'); // Ensure no invalid styles remain
                });
                return isValid;
            }


            function generateSummary() {
                 // No validation needed anymore before generating summary
                 const mainElement = document.querySelector('main');
                const summarySection = document.querySelector('.summary-section');
                const summaryContent = document.getElementById('summary-content');

                summaryContent.innerHTML = ''; // Clear previous summary

                // --- Add Header for Print/Summary ---
                const printHeaderContainer = document.createElement('div');
                printHeaderContainer.className = 'print-header';

     

                // Add Title
                const printHeaderTitle = document.createElement('div');
                printHeaderTitle.className = 'print-header-title';
                printHeaderTitle.textContent = "استمارة حجز وحدة سكنية - مجمع المؤمل السكني";
                printHeaderContainer.appendChild(printHeaderTitle);


                summaryContent.appendChild(printHeaderContainer);
                // --- End Add Header ---


                const dataColumnsContainer = document.createElement('div');
                dataColumnsContainer.className = 'summary-data-columns';
                summaryContent.appendChild(dataColumnsContainer);

                // Iterate through sectionsConfig to build the summary content
                sectionsConfig.forEach(sectionInfo => {
                    const sectionDiv = document.createElement('div');
                    sectionDiv.className = 'summary-section-group';
                    sectionDiv.innerHTML = `<h3>${sectionInfo.title}</h3>`;

                    sectionInfo.fields.forEach(fieldInfo => {
                        const fieldElement = document.getElementById(fieldInfo.id);
                        if (fieldElement) {
                            let valueText = ''; // Display value
                            let rawValue = fieldElement.value ? fieldElement.value.trim() : ''; // Raw input value

                            if (fieldInfo.type === 'select') {
                                valueText = (fieldElement.selectedIndex >= 0 && fieldElement.value)
                                    ? fieldElement.options[fieldElement.selectedIndex].text
                                    : '';
                            } else if (fieldInfo.type === 'currency') {
                                // Format currency only if there's a valid number
                                const numValue = parseFloat(rawValue);
                                valueText = !isNaN(numValue) && numValue !== 0
                                    ? `${numValue.toLocaleString('ar-IQ')} دينار عراقي`
                                    : ''; // Display empty if 0 or invalid
                            } else if (fieldInfo.type === 'textarea') {
                                valueText = rawValue; // Use raw value for textarea
                            }
                            else {
                                valueText = rawValue; // Default for text, tel, etc.
                            }

                            // Use placeholder if value is empty
                            const displayValue = valueText || '<i>لم يتم إدخاله</i>';

                            const fieldDiv = document.createElement('div');
                            fieldDiv.className = 'summary-field';
                             // Add 'notes' class for specific styling if it's a textarea field
                            if (fieldInfo.type === 'textarea' && fieldInfo.id === 'custom-notes') {
                                fieldDiv.classList.add('notes');
                            }
                            fieldDiv.innerHTML = `
                                <label>${fieldInfo.label}:</label>
                                <span>${displayValue}</span>
                            `;
                            sectionDiv.appendChild(fieldDiv);
                        }
                    });
                    // Only append the section group if it contains fields (it always will based on config)
                    dataColumnsContainer.appendChild(sectionDiv);
                });


                // Clone and append Important Notes
                const importantNotesSection = document.querySelector('.important-notes-section');
                if (importantNotesSection) {
                    const importantNotesClone = importantNotesSection.cloneNode(true);
                    // Clean up classes for print/summary view
                    importantNotesClone.classList.remove('important-notes-section', 'b-border-box');
                    importantNotesClone.classList.add('important-notes-print');
                    importantNotesClone.removeAttribute('id'); // Remove ID if exists
                    const notesHeader = importantNotesClone.querySelector('h3');
                    if(notesHeader) notesHeader.textContent = "الملاحظات الهامة"; // Standardize header
                    summaryContent.appendChild(importantNotesClone);
                }

                // Add Signature Section
                const signatureSection = document.createElement('div');
                signatureSection.className = 'signature-section-print';
                signatureSection.innerHTML = `
                    <h4>التواقيع</h4>
                    <div class="signature-boxes">
                        <div class="signature-box"><label>مـمثـل قـسم المـبيـعات:</label><span></span></div>
                        <div class="signature-box"><label>أميـن الـصندوق:</label><span></span></div>
                        <div class="signature-box"><label>مـمثـل قـسم الـحجوزات:</label><span></span></div>
                        <div class="signature-box"><label>أسـم وتـوقيـع وبـصمة الـزبـون:</label><span></span></div>
                    </div>
                `;
                summaryContent.appendChild(signatureSection);

                // Hide form sections and show summary
                mainElement.querySelectorAll('section:not(.summary-section)').forEach(sec => sec.hidden = true);
                summarySection.hidden = false;
                isPreviewActive = true;

                // Update button visibility
                document.getElementById('preview-button').classList.remove('active');
                document.getElementById('preview-button').hidden = true;
                document.getElementById('back-to-form-button').classList.add('active');
                document.getElementById('back-to-form-button').hidden = false;
                document.getElementById('whatsapp-button').classList.add('active'); // Show WhatsApp button in preview
                document.getElementById('whatsapp-button').hidden = false;

                // Scroll to the summary section smoothly
                summarySection.scrollIntoView({ behavior: 'smooth' });
            }

            function restoreFormView() {
                const mainElement = document.querySelector('main');
                const summarySection = document.querySelector('.summary-section');

                // Show form sections
                mainElement.querySelectorAll('section:not(.summary-section)').forEach(sec => sec.hidden = false);

                // Hide summary section
                if (summarySection) {
                    summarySection.hidden = true;
                }

                isPreviewActive = false;

                // Update button visibility
                document.getElementById('preview-button').classList.add('active');
                document.getElementById('preview-button').hidden = false;
                document.getElementById('back-to-form-button').classList.remove('active');
                document.getElementById('back-to-form-button').hidden = true;
                document.getElementById('whatsapp-button').classList.remove('active'); // Hide WhatsApp button
                document.getElementById('whatsapp-button').hidden = true;

                // Scroll back to top might be useful
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function generateWhatsAppMessage() {
                let message = `*استمارة حجز وحدة سكنية - مجمع المؤمل السكني*\n------------------------------------\n\n`;

                sectionsConfig.forEach(sectionInfo => {
                    message += `*${sectionInfo.title}*\n`;
                    let sectionHasData = false;

                    sectionInfo.fields.forEach(fieldInfo => {
                        const fieldElement = document.getElementById(fieldInfo.id);
                        if (fieldElement) {
                            let value = '';
                            if (fieldInfo.type === 'select') {
                                value = (fieldElement.selectedIndex >= 0 && fieldElement.value)
                                    ? fieldElement.options[fieldElement.selectedIndex].text.trim()
                                    : '';
                            } else if (fieldInfo.type === 'currency') {
                                const numValue = parseFloat(fieldElement.value);
                                value = !isNaN(numValue) && numValue !== 0
                                     ? `${numValue.toLocaleString('ar-IQ')} دينار`
                                     : '';
                            } else {
                                value = fieldElement.value ? fieldElement.value.trim() : '';
                            }

                            if (value) { // Only include fields with a value
                                message += `${fieldInfo.label}: ${value}\n`;
                                sectionHasData = true;
                            }
                        }
                    });
                    if (sectionHasData) {
                        message += `\n`; // Add spacing between sections only if it had data
                    } else {
                         // Optional: Remove the section title if no data was found
                         // message = message.replace(`*${sectionInfo.title}*\n`, '');
                    }
                });

                // Add Important Notes to WhatsApp Message only if they exist
                 const importantNotesList = document.querySelector('.important-notes-section ol');
                 if (importantNotesList && importantNotesList.querySelectorAll('li').length > 0) {
                    message += `*الملاحظات الهامة*\n`;
                    const notesItems = importantNotesList.querySelectorAll('li');
                    notesItems.forEach((item, index) => {
                        message += `${index + 1}. ${item.textContent.trim()}\n`;
                    });
                    message += `\n`;
                 }


                message += `------------------------------------`;
                return message.trim(); // Trim trailing whitespace
            }


            function sendViaWhatsApp() {
                // First, ensure we are in preview mode (meaning data is generated)
                // Or, alternatively, generate the message directly if needed.
                if (!isPreviewActive) {
                     alert("يرجى الضغط على 'معاينة المعلومات' أولاً لتجهيز البيانات للإرسال.");
                     return;
                }

                const customerPhoneInput = document.getElementById('cust-phone1');
                let customerPhone = customerPhoneInput ? customerPhoneInput.value.trim() : null;

                // Since phone is optional, don't alert if empty, but also don't proceed
                if (!customerPhone) {
                    alert("لا يمكن الإرسال عبر واتساب بدون رقم هاتف الزبون الأول.");
                    return;
                }

                // Basic phone number cleaning (adjust based on expected formats)
                 customerPhone = customerPhone.replace(/[\s+\-\(\)]/g, ''); // Remove spaces, +, -, ()
                // Simple check if it looks like an Iraqi number (starts with 07 or 9647)
                 if (customerPhone.startsWith('07') && customerPhone.length >= 11) {
                    // Convert to international format (optional, depends on WhatsApp API needs)
                     // customerPhone = '964' + customerPhone.substring(1);
                 } else if (customerPhone.startsWith('9647') && customerPhone.length >= 13) {
                     // Already in international format potentially
                 }
                 else {
                      // Alert if a number is present but doesn't match format
                      alert("صيغة رقم هاتف الزبون المدخل غير صحيحة. يرجى التحقق.");
                      customerPhoneInput?.focus();
                      return;
                 }


                const message = generateWhatsAppMessage();
                const encodedMessage = encodeURIComponent(message);

                // Decide whether to send TO the customer or TO the complex number
                // Sending TO the customer:
                const whatsappUrl = `https://wa.me/${customerPhone}?text=${encodedMessage}`;
                // Sending TO the complex (replace COMPLEX_WHATSAPP_NUMBER):
                // const whatsappUrl = `https://wa.me/${COMPLEX_WHATSAPP_NUMBER}?text=${encodedMessage}`;

                console.log("WhatsApp URL:", whatsappUrl); // Debugging
                console.log("Message Length:", message.length); // Debugging length

                if (message.length === 0) {
                    alert("لا يوجد بيانات لإرسالها. يرجى ملء النموذج أولاً.");
                    return;
                }

                // Open WhatsApp link in a new tab/window
                window.open(whatsappUrl, '_blank');
            }

            // Add event listeners when the DOM is fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                setCurrentDates(); // Set initial date

                // Get buttons
                const previewButton = document.getElementById('preview-button');
                const printButton = document.getElementById('print-button');
                const clearButton = document.getElementById('clear-button');
                const backButton = document.getElementById('back-to-form-button');
                const whatsappButton = document.getElementById('whatsapp-button');

                // Attach event listeners
                if (previewButton) previewButton.addEventListener('click', generateSummary);
                if (printButton) printButton.addEventListener('click', printPage);
                if (clearButton) clearButton.addEventListener('click', clearForms);
                if (backButton) backButton.addEventListener('click', restoreFormView);
                if (whatsappButton) whatsappButton.addEventListener('click', sendViaWhatsApp);

                // Set initial button states
                if(previewButton) { previewButton.classList.add('active'); previewButton.hidden = false; }
                if(printButton) { printButton.classList.add('active'); printButton.hidden = false; }
                if(clearButton) { clearButton.classList.add('active'); clearButton.hidden = false; }
                if(backButton) { backButton.classList.remove('active'); backButton.hidden = true; }
                if(whatsappButton) { whatsappButton.classList.remove('active'); whatsappButton.hidden = true; }
            });
        </script>
    </div>
</body>
</html>